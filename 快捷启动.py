#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
智能裁剪处理器 V2 快捷启动脚本
支持拖拽文件/文件夹，自动处理
"""

import os
import sys
import tkinter as tk
from tkinter import filedialog, messagebox, ttk
import threading
import subprocess
from pathlib import Path

class QuickLauncher:
    def __init__(self):
        self.root = tk.Tk()
        self.root.title("智能裁剪处理器 V2 - 快捷启动")
        self.root.geometry("600x400")
        self.root.configure(bg='#f0f0f0')
        
        # 设置样式
        self.style = ttk.Style()
        self.style.theme_use('clam')
        
        self.setup_ui()
        
    def setup_ui(self):
        """设置用户界面"""
        # 标题
        title_frame = tk.Frame(self.root, bg='#f0f0f0')
        title_frame.pack(pady=20)
        
        title_label = tk.Label(
            title_frame, 
            text="🚀 智能裁剪处理器 V2", 
            font=("Arial", 18, "bold"),
            bg='#f0f0f0',
            fg='#2c3e50'
        )
        title_label.pack()
        
        subtitle_label = tk.Label(
            title_frame, 
            text="SAM主导 · 自动批量处理 · 无置信度干扰", 
            font=("Arial", 10),
            bg='#f0f0f0',
            fg='#7f8c8d'
        )
        subtitle_label.pack(pady=5)
        
        # 拖拽区域
        self.drop_frame = tk.Frame(
            self.root, 
            bg='#ecf0f1', 
            relief='dashed', 
            bd=2,
            height=150
        )
        self.drop_frame.pack(pady=20, padx=40, fill='x')
        self.drop_frame.pack_propagate(False)
        
        drop_label = tk.Label(
            self.drop_frame,
            text="📁 拖拽图片文件或文件夹到这里\n或点击下方按钮选择",
            font=("Arial", 12),
            bg='#ecf0f1',
            fg='#34495e',
            justify='center'
        )
        drop_label.pack(expand=True)
        
        # 按钮区域
        button_frame = tk.Frame(self.root, bg='#f0f0f0')
        button_frame.pack(pady=20)
        
        # 选择文件按钮
        self.select_file_btn = ttk.Button(
            button_frame,
            text="📄 选择图片文件",
            command=self.select_file,
            width=20
        )
        self.select_file_btn.pack(side='left', padx=10)
        
        # 选择文件夹按钮
        self.select_folder_btn = ttk.Button(
            button_frame,
            text="📁 选择文件夹",
            command=self.select_folder,
            width=20
        )
        self.select_folder_btn.pack(side='left', padx=10)
        
        # 处理按钮
        self.process_btn = ttk.Button(
            button_frame,
            text="🚀 开始处理",
            command=self.start_processing,
            width=20,
            state='disabled'
        )
        self.process_btn.pack(side='left', padx=10)
        
        # 状态显示区域
        status_frame = tk.Frame(self.root, bg='#f0f0f0')
        status_frame.pack(pady=20, padx=40, fill='both', expand=True)
        
        status_label = tk.Label(
            status_frame,
            text="📋 处理状态:",
            font=("Arial", 10, "bold"),
            bg='#f0f0f0',
            fg='#2c3e50'
        )
        status_label.pack(anchor='w')
        
        # 状态文本框
        self.status_text = tk.Text(
            status_frame,
            height=8,
            font=("Consolas", 9),
            bg='#2c3e50',
            fg='#ecf0f1',
            insertbackground='#ecf0f1'
        )
        self.status_text.pack(fill='both', expand=True, pady=5)
        
        # 滚动条
        scrollbar = ttk.Scrollbar(self.status_text)
        scrollbar.pack(side='right', fill='y')
        self.status_text.config(yscrollcommand=scrollbar.set)
        scrollbar.config(command=self.status_text.yview)
        
        # 初始状态
        self.selected_path = None
        self.log_message("🎯 请选择要处理的图片文件或文件夹")
        
        # 绑定拖拽事件
        self.setup_drag_drop()
    
    def setup_drag_drop(self):
        """设置拖拽功能"""
        try:
            from tkinterdnd2 import DND_FILES, TkinterDnD
            
            # 将root转换为支持拖拽的窗口
            self.root = TkinterDnD.Tk()
            self.root.title("智能裁剪处理器 V2 - 快捷启动")
            self.root.geometry("600x400")
            self.root.configure(bg='#f0f0f0')
            
            # 重新设置UI
            for widget in self.root.winfo_children():
                widget.destroy()
            self.setup_ui()
            
            # 绑定拖拽
            self.drop_frame.drop_target_register(DND_FILES)
            self.drop_frame.dnd_bind('<<Drop>>', self.on_drop)
            
        except ImportError:
            self.log_message("⚠️  拖拽功能需要安装 tkinterdnd2: pip install tkinterdnd2")
    
    def on_drop(self, event):
        """处理拖拽事件"""
        files = self.root.tk.splitlist(event.data)
        if files:
            self.selected_path = files[0]
            self.log_message(f"📁 已选择: {self.selected_path}")
            self.process_btn.config(state='normal')
    
    def select_file(self):
        """选择单个文件"""
        file_path = filedialog.askopenfilename(
            title="选择图片文件",
            filetypes=[
                ("图片文件", "*.jpg *.jpeg *.png *.bmp *.tiff *.webp"),
                ("所有文件", "*.*")
            ]
        )
        
        if file_path:
            self.selected_path = file_path
            self.log_message(f"📄 已选择文件: {Path(file_path).name}")
            self.process_btn.config(state='normal')
    
    def select_folder(self):
        """选择文件夹"""
        folder_path = filedialog.askdirectory(title="选择包含图片的文件夹")
        
        if folder_path:
            self.selected_path = folder_path
            self.log_message(f"📁 已选择文件夹: {Path(folder_path).name}")
            self.process_btn.config(state='normal')
    
    def log_message(self, message):
        """添加日志消息"""
        self.status_text.insert(tk.END, f"{message}\n")
        self.status_text.see(tk.END)
        self.root.update()
    
    def start_processing(self):
        """开始处理"""
        if not self.selected_path:
            messagebox.showwarning("警告", "请先选择文件或文件夹")
            return
        
        # 禁用按钮
        self.process_btn.config(state='disabled')
        self.select_file_btn.config(state='disabled')
        self.select_folder_btn.config(state='disabled')
        
        # 清空状态
        self.status_text.delete(1.0, tk.END)
        self.log_message("🚀 开始处理...")
        
        # 在新线程中运行处理
        thread = threading.Thread(target=self.run_processing)
        thread.daemon = True
        thread.start()
    
    def run_processing(self):
        """运行处理程序"""
        try:
            # 构建命令
            script_path = "improved_processor/智能裁剪处理器_v2.py"
            cmd = [sys.executable, script_path, self.selected_path]
            
            self.log_message(f"📋 执行命令: {' '.join(cmd)}")
            
            # 运行处理程序
            process = subprocess.Popen(
                cmd,
                stdout=subprocess.PIPE,
                stderr=subprocess.STDOUT,
                text=True,
                bufsize=1,
                universal_newlines=True
            )
            
            # 实时显示输出
            for line in process.stdout:
                line = line.strip()
                if line:
                    self.log_message(line)
            
            # 等待完成
            process.wait()
            
            if process.returncode == 0:
                self.log_message("🎉 处理完成！")
                
                # 显示输出目录
                if os.path.isfile(self.selected_path):
                    output_dir = Path(self.selected_path).parent / "cropped_output"
                else:
                    output_dir = Path(self.selected_path) / "cropped_output"
                
                if output_dir.exists():
                    self.log_message(f"📂 输出目录: {output_dir}")
                    
                    # 询问是否打开输出目录
                    if messagebox.askyesno("完成", f"处理完成！\n是否打开输出目录？\n{output_dir}"):
                        if sys.platform == "darwin":  # macOS
                            subprocess.run(["open", str(output_dir)])
                        elif sys.platform == "win32":  # Windows
                            subprocess.run(["explorer", str(output_dir)])
                        else:  # Linux
                            subprocess.run(["xdg-open", str(output_dir)])
            else:
                self.log_message(f"❌ 处理失败，返回码: {process.returncode}")
                
        except Exception as e:
            self.log_message(f"❌ 处理出错: {e}")
        
        finally:
            # 重新启用按钮
            self.process_btn.config(state='normal')
            self.select_file_btn.config(state='normal')
            self.select_folder_btn.config(state='normal')
    
    def run(self):
        """运行应用"""
        self.root.mainloop()

def main():
    """主函数"""
    try:
        app = QuickLauncher()
        app.run()
    except Exception as e:
        print(f"启动失败: {e}")
        input("按回车键退出...")

if __name__ == "__main__":
    main()

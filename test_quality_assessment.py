#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
质量评估系统测试脚本
测试新的检测质量验证和置信度评估功能
"""

import sys
import os
from pathlib import Path

# 添加improved_processor到路径
sys.path.append('improved_processor')

def test_quality_assessment():
    """测试质量评估系统"""
    try:
        from 智能分层裁剪处理器 import SmartLayeredProcessor
        
        print("🧪 开始测试质量评估系统...")
        
        # 初始化处理器
        processor = SmartLayeredProcessor()
        
        # 测试置信度计算
        print("\n📊 测试置信度计算:")
        
        # 测试MediaPipe置信度
        mp_confidence = processor.calculate_detection_confidence(
            "neck_mediapipe", 
            True, 
            100, 
            {'visibility': 0.8}
        )
        print(f"MediaPipe置信度 (visibility=0.8): {mp_confidence:.3f}")
        
        # 测试SAM置信度
        sam_confidence = processor.calculate_detection_confidence(
            "neck_sam", 
            True, 
            100, 
            {'sam_score': 0.9, 'mask_area': 5000}
        )
        print(f"SAM置信度 (score=0.9): {sam_confidence:.3f}")
        
        # 测试Haar置信度
        haar_confidence = processor.calculate_detection_confidence(
            "neck_haar", 
            True, 
            100, 
            {'face_area_ratio': 0.15, 'face_count': 1}
        )
        print(f"Haar置信度 (face_ratio=0.15): {haar_confidence:.3f}")
        
        # 测试质量验证
        print("\n🔍 测试质量验证:")
        
        # 模拟检测结果
        mock_results = [
            ("neck_haar", True, 120, 0.8),
            ("neck_mediapipe", True, 125, 0.7),
            ("neck_sam", True, 130, 0.6),
        ]
        
        # 创建一个临时图像路径用于测试
        test_image_path = "test_image.jpg"
        
        # 由于我们没有真实图像，我们需要模拟validate_detection_quality的行为
        print("模拟质量验证结果:")
        print(f"检测结果: {mock_results}")
        
        # 计算位置一致性
        positions = [result for _, success, result, _ in mock_results if success]
        confidences = [conf for _, success, _, conf in mock_results if success]
        
        print(f"检测位置: {positions}")
        print(f"置信度: {confidences}")
        
        # 计算位置标准差
        if len(positions) > 1:
            import statistics
            avg_pos = statistics.mean(positions)
            pos_std = statistics.stdev(positions)
            relative_std = pos_std / avg_pos if avg_pos > 0 else 0
            print(f"位置标准差: {pos_std:.2f} (相对: {relative_std:.3f})")
        
        avg_confidence = sum(confidences) / len(confidences)
        print(f"平均置信度: {avg_confidence:.3f}")
        
        print("\n✅ 质量评估系统测试完成!")
        
        return True
        
    except Exception as e:
        print(f"❌ 测试失败: {e}")
        import traceback
        traceback.print_exc()
        return False

def test_with_real_image():
    """如果有真实图像，测试完整流程"""
    try:
        # 查找测试图像
        test_dirs = ['test_images', 'samples', '.']
        test_image = None
        
        for test_dir in test_dirs:
            if os.path.exists(test_dir):
                for ext in ['*.jpg', '*.jpeg', '*.png']:
                    import glob
                    images = glob.glob(os.path.join(test_dir, ext))
                    if images:
                        test_image = images[0]
                        break
                if test_image:
                    break
        
        if test_image and os.path.exists(test_image):
            print(f"\n🖼️  找到测试图像: {test_image}")
            
            from 智能分层裁剪处理器 import SmartLayeredProcessor
            processor = SmartLayeredProcessor()
            
            print("开始完整质量评估测试...")
            success, result = processor.process_single_image_smart(test_image)
            
            if success:
                print(f"✅ 处理成功: {result}")
            else:
                print(f"❌ 处理失败: {result}")
                
        else:
            print("ℹ️  未找到测试图像，跳过真实图像测试")
            
    except Exception as e:
        print(f"❌ 真实图像测试失败: {e}")

if __name__ == "__main__":
    print("🔬 质量评估系统测试")
    print("=" * 50)
    
    # 基础功能测试
    test_quality_assessment()
    
    # 真实图像测试（如果有的话）
    test_with_real_image()
    
    print("\n🎯 测试完成!")

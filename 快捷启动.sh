#!/bin/bash

# 智能裁剪处理器 V2 快捷启动脚本
# 使用方法: ./快捷启动.sh

echo "🚀 智能裁剪处理器 V2 - 快捷启动"
echo "=================================================="

# 检查处理器脚本是否存在
PROCESSOR_SCRIPT="improved_processor/智能裁剪处理器_v2.py"
if [ ! -f "$PROCESSOR_SCRIPT" ]; then
    echo "❌ 处理器脚本不存在: $PROCESSOR_SCRIPT"
    exit 1
fi

# 显示菜单
show_menu() {
    echo ""
    echo "📋 请选择处理方式:"
    echo "1. 处理当前目录下的所有图片"
    echo "2. 处理指定文件"
    echo "3. 处理指定目录"
    echo "4. 退出"
    echo "----------------------------------------"
}

# 处理当前目录
process_current_dir() {
    echo "📁 扫描当前目录的图片文件..."
    
    # 统计图片文件数量
    count=$(find . -maxdepth 1 \( -iname "*.jpg" -o -iname "*.jpeg" -o -iname "*.png" -o -iname "*.bmp" -o -iname "*.tiff" -o -iname "*.webp" \) | wc -l)
    
    if [ $count -eq 0 ]; then
        echo "❌ 当前目录下没有找到图片文件"
        return
    fi
    
    echo "📊 找到 $count 张图片"
    
    # 显示前几个文件
    echo "📄 图片列表:"
    find . -maxdepth 1 \( -iname "*.jpg" -o -iname "*.jpeg" -o -iname "*.png" -o -iname "*.bmp" -o -iname "*.tiff" -o -iname "*.webp" \) | head -5 | while read file; do
        echo "   $(basename "$file")"
    done
    
    if [ $count -gt 5 ]; then
        echo "   ... 还有 $((count - 5)) 张图片"
    fi
    
    echo ""
    read -p "🤔 确认处理这 $count 张图片吗? (y/N): " confirm
    
    if [[ $confirm =~ ^[Yy]$ ]]; then
        echo "🚀 开始处理当前目录..."
        python3 "$PROCESSOR_SCRIPT" "."
        
        if [ $? -eq 0 ]; then
            echo "🎉 处理完成！"
            if [ -d "cropped_output" ]; then
                echo "📂 输出目录: cropped_output"
                echo "📊 生成文件数: $(ls cropped_output | wc -l)"
                
                read -p "🤔 是否打开输出目录? (y/N): " open_dir
                if [[ $open_dir =~ ^[Yy]$ ]]; then
                    open cropped_output 2>/dev/null || xdg-open cropped_output 2>/dev/null || echo "请手动打开 cropped_output 目录"
                fi
            fi
        else
            echo "❌ 处理失败"
        fi
    else
        echo "❌ 取消处理"
    fi
}

# 处理指定文件
process_file() {
    echo ""
    read -p "📄 请输入图片文件路径: " file_path
    
    # 去掉引号
    file_path=$(echo "$file_path" | sed 's/^"//;s/"$//')
    
    if [ -z "$file_path" ]; then
        echo "❌ 路径不能为空"
        return
    fi
    
    if [ ! -f "$file_path" ]; then
        echo "❌ 文件不存在: $file_path"
        return
    fi
    
    echo "🚀 开始处理文件: $(basename "$file_path")"
    python3 "$PROCESSOR_SCRIPT" "$file_path"
    
    if [ $? -eq 0 ]; then
        echo "🎉 处理完成！"
        
        # 确定输出目录
        dir_path=$(dirname "$file_path")
        output_dir="$dir_path/cropped_output"
        
        if [ -d "$output_dir" ]; then
            echo "📂 输出目录: $output_dir"
            echo "📊 生成文件数: $(ls "$output_dir" | wc -l)"
            
            read -p "🤔 是否打开输出目录? (y/N): " open_dir
            if [[ $open_dir =~ ^[Yy]$ ]]; then
                open "$output_dir" 2>/dev/null || xdg-open "$output_dir" 2>/dev/null || echo "请手动打开 $output_dir 目录"
            fi
        fi
    else
        echo "❌ 处理失败"
    fi
}

# 处理指定目录
process_directory() {
    echo ""
    read -p "📁 请输入目录路径: " dir_path
    
    # 去掉引号
    dir_path=$(echo "$dir_path" | sed 's/^"//;s/"$//')
    
    if [ -z "$dir_path" ]; then
        echo "❌ 路径不能为空"
        return
    fi
    
    if [ ! -d "$dir_path" ]; then
        echo "❌ 目录不存在: $dir_path"
        return
    fi
    
    # 统计目录中的图片文件
    count=$(find "$dir_path" -maxdepth 1 \( -iname "*.jpg" -o -iname "*.jpeg" -o -iname "*.png" -o -iname "*.bmp" -o -iname "*.tiff" -o -iname "*.webp" \) | wc -l)
    
    if [ $count -eq 0 ]; then
        echo "❌ 目录中没有找到图片文件"
        return
    fi
    
    echo "📊 找到 $count 张图片"
    echo "🚀 开始处理目录: $(basename "$dir_path")"
    python3 "$PROCESSOR_SCRIPT" "$dir_path"
    
    if [ $? -eq 0 ]; then
        echo "🎉 处理完成！"
        
        output_dir="$dir_path/cropped_output"
        if [ -d "$output_dir" ]; then
            echo "📂 输出目录: $output_dir"
            echo "📊 生成文件数: $(ls "$output_dir" | wc -l)"
            
            read -p "🤔 是否打开输出目录? (y/N): " open_dir
            if [[ $open_dir =~ ^[Yy]$ ]]; then
                open "$output_dir" 2>/dev/null || xdg-open "$output_dir" 2>/dev/null || echo "请手动打开 $output_dir 目录"
            fi
        fi
    else
        echo "❌ 处理失败"
    fi
}

# 主循环
while true; do
    show_menu
    read -p "请选择 (1-4): " choice
    
    case $choice in
        1)
            process_current_dir
            ;;
        2)
            process_file
            ;;
        3)
            process_directory
            ;;
        4)
            echo "👋 再见！"
            exit 0
            ;;
        *)
            echo "❌ 无效选择，请输入 1-4"
            continue
            ;;
    esac
    
    echo ""
    read -p "🤔 是否继续处理其他文件? (y/N): " continue_choice
    if [[ ! $continue_choice =~ ^[Yy]$ ]]; then
        echo "👋 再见！"
        break
    fi
done

#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
智能裁剪处理器 V2 - 快速处理脚本
简单易用的命令行界面
"""

import os
import sys
import glob
from pathlib import Path
import subprocess

def print_banner():
    """打印横幅"""
    print("=" * 60)
    print("🚀 智能裁剪处理器 V2 - 快速处理")
    print("   SAM主导 · 自动批量处理 · 无置信度干扰")
    print("=" * 60)

def find_images_in_current_dir():
    """在当前目录查找图片文件"""
    image_extensions = ['*.jpg', '*.jpeg', '*.png', '*.bmp', '*.tiff', '*.webp']
    image_files = []
    
    for ext in image_extensions:
        image_files.extend(glob.glob(ext))
        image_files.extend(glob.glob(ext.upper()))
    
    return sorted(image_files)

def show_menu():
    """显示菜单"""
    print("\n📋 请选择处理方式:")
    print("1. 处理当前目录下的所有图片")
    print("2. 处理指定文件")
    print("3. 处理指定目录")
    print("4. 退出")
    print("-" * 40)

def process_current_directory():
    """处理当前目录"""
    images = find_images_in_current_dir()
    
    if not images:
        print("❌ 当前目录下没有找到图片文件")
        return False
    
    print(f"📁 找到 {len(images)} 张图片:")
    for i, img in enumerate(images[:5], 1):  # 只显示前5个
        print(f"   {i}. {img}")
    if len(images) > 5:
        print(f"   ... 还有 {len(images) - 5} 张图片")
    
    confirm = input(f"\n🤔 确认处理这 {len(images)} 张图片吗? (y/N): ").strip().lower()
    if confirm not in ['y', 'yes', '是']:
        print("❌ 取消处理")
        return False
    
    return run_processor(".")

def process_specific_file():
    """处理指定文件"""
    file_path = input("📄 请输入图片文件路径: ").strip().strip('"')
    
    if not file_path:
        print("❌ 路径不能为空")
        return False
    
    if not os.path.isfile(file_path):
        print(f"❌ 文件不存在: {file_path}")
        return False
    
    return run_processor(file_path)

def process_specific_directory():
    """处理指定目录"""
    dir_path = input("📁 请输入目录路径: ").strip().strip('"')
    
    if not dir_path:
        print("❌ 路径不能为空")
        return False
    
    if not os.path.isdir(dir_path):
        print(f"❌ 目录不存在: {dir_path}")
        return False
    
    return run_processor(dir_path)

def run_processor(input_path):
    """运行处理器"""
    try:
        script_path = "improved_processor/智能裁剪处理器_v2.py"
        
        if not os.path.exists(script_path):
            print(f"❌ 处理器脚本不存在: {script_path}")
            return False
        
        print(f"\n🚀 开始处理: {input_path}")
        print("-" * 40)
        
        # 运行处理器
        cmd = [sys.executable, script_path, input_path]
        result = subprocess.run(cmd, capture_output=False, text=True)
        
        print("-" * 40)
        
        if result.returncode == 0:
            print("🎉 处理完成！")
            
            # 显示输出目录
            if os.path.isfile(input_path):
                output_dir = Path(input_path).parent / "cropped_output"
            else:
                output_dir = Path(input_path) / "cropped_output"
            
            if output_dir.exists():
                print(f"📂 输出目录: {output_dir}")
                
                # 统计输出文件
                output_files = list(output_dir.glob("*"))
                if output_files:
                    print(f"📊 生成了 {len(output_files)} 个文件")
                
                # 询问是否打开目录
                open_dir = input("🤔 是否打开输出目录? (y/N): ").strip().lower()
                if open_dir in ['y', 'yes', '是']:
                    try:
                        if sys.platform == "darwin":  # macOS
                            subprocess.run(["open", str(output_dir)])
                        elif sys.platform == "win32":  # Windows
                            subprocess.run(["explorer", str(output_dir)])
                        else:  # Linux
                            subprocess.run(["xdg-open", str(output_dir)])
                        print("📂 已打开输出目录")
                    except Exception as e:
                        print(f"❌ 打开目录失败: {e}")
            
            return True
        else:
            print(f"❌ 处理失败，返回码: {result.returncode}")
            return False
            
    except Exception as e:
        print(f"❌ 运行出错: {e}")
        return False

def main():
    """主函数"""
    print_banner()
    
    while True:
        show_menu()
        
        try:
            choice = input("请选择 (1-4): ").strip()
            
            if choice == '1':
                process_current_directory()
            elif choice == '2':
                process_specific_file()
            elif choice == '3':
                process_specific_directory()
            elif choice == '4':
                print("👋 再见！")
                break
            else:
                print("❌ 无效选择，请输入 1-4")
                continue
            
            # 询问是否继续
            continue_choice = input("\n🤔 是否继续处理其他文件? (y/N): ").strip().lower()
            if continue_choice not in ['y', 'yes', '是']:
                print("👋 再见！")
                break
                
        except KeyboardInterrupt:
            print("\n\n👋 用户取消，再见！")
            break
        except Exception as e:
            print(f"❌ 出错: {e}")

if __name__ == "__main__":
    main()

#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
智能裁剪处理器 V2 - 一键测试脚本
自动创建测试图片并演示处理效果
"""

import os
import sys
import subprocess
from pathlib import Path
from PIL import Image, ImageDraw, ImageFont

def create_test_images():
    """创建多种类型的测试图片"""
    test_dir = Path("test_images")
    test_dir.mkdir(exist_ok=True)
    
    print("🎨 创建测试图片...")
    
    # 测试图片1：标准人像（全身）
    create_full_body_image(test_dir / "test_full_body.jpg")
    
    # 测试图片2：半身像
    create_half_body_image(test_dir / "test_half_body.jpg")
    
    # 测试图片3：头像
    create_head_only_image(test_dir / "test_head_only.jpg")
    
    print(f"✅ 测试图片已创建在: {test_dir}")
    return test_dir

def create_full_body_image(path):
    """创建全身人像测试图片"""
    width, height = 400, 600
    image = Image.new('RGB', (width, height), (240, 240, 240))
    draw = ImageDraw.Draw(image)
    
    # 头部
    head_center = (width//2, height//5)
    head_width, head_height = 70, 90
    draw.ellipse([
        head_center[0] - head_width//2, head_center[1] - head_height//2,
        head_center[0] + head_width//2, head_center[1] + head_height//2
    ], fill=(255, 220, 177))
    
    # 五官
    draw.ellipse([head_center[0] - 20, head_center[1] - 15, head_center[0] - 10, head_center[1] - 5], fill=(0, 0, 0))
    draw.ellipse([head_center[0] + 10, head_center[1] - 15, head_center[0] + 20, head_center[1] - 5], fill=(0, 0, 0))
    draw.ellipse([head_center[0] - 3, head_center[1], head_center[0] + 3, head_center[1] + 8], fill=(200, 180, 150))
    
    # 脖子
    neck_top = head_center[1] + head_height//2
    neck_bottom = neck_top + 40
    draw.rectangle([head_center[0] - 20, neck_top, head_center[0] + 20, neck_bottom], fill=(255, 220, 177))
    
    # 肩膀和身体
    shoulder_top = neck_bottom
    shoulder_width = 120
    body_height = 200
    draw.rectangle([
        head_center[0] - shoulder_width//2, shoulder_top,
        head_center[0] + shoulder_width//2, shoulder_top + body_height
    ], fill=(100, 150, 200))
    
    # 手臂
    arm_width = 25
    draw.rectangle([
        head_center[0] - shoulder_width//2 - arm_width, shoulder_top + 10,
        head_center[0] - shoulder_width//2, shoulder_top + 120
    ], fill=(255, 220, 177))
    draw.rectangle([
        head_center[0] + shoulder_width//2, shoulder_top + 10,
        head_center[0] + shoulder_width//2 + arm_width, shoulder_top + 120
    ], fill=(255, 220, 177))
    
    # 腿部
    leg_width = 40
    leg_height = 180
    draw.rectangle([
        head_center[0] - leg_width, shoulder_top + body_height,
        head_center[0], shoulder_top + body_height + leg_height
    ], fill=(50, 50, 150))
    draw.rectangle([
        head_center[0], shoulder_top + body_height,
        head_center[0] + leg_width, shoulder_top + body_height + leg_height
    ], fill=(50, 50, 150))
    
    # 添加标签
    try:
        font = ImageFont.truetype("Arial.ttf", 16)
    except:
        font = ImageFont.load_default()
    
    draw.text((10, 10), "全身人像测试", fill=(0, 0, 0), font=font)
    
    image.save(path, quality=95)
    print(f"   ✅ 全身人像: {path.name}")

def create_half_body_image(path):
    """创建半身人像测试图片"""
    width, height = 400, 400
    image = Image.new('RGB', (width, height), (240, 240, 240))
    draw = ImageDraw.Draw(image)
    
    # 头部
    head_center = (width//2, height//4)
    head_width, head_height = 80, 100
    draw.ellipse([
        head_center[0] - head_width//2, head_center[1] - head_height//2,
        head_center[0] + head_width//2, head_center[1] + head_height//2
    ], fill=(255, 220, 177))
    
    # 五官
    draw.ellipse([head_center[0] - 25, head_center[1] - 20, head_center[0] - 15, head_center[1] - 10], fill=(0, 0, 0))
    draw.ellipse([head_center[0] + 15, head_center[1] - 20, head_center[0] + 25, head_center[1] - 10], fill=(0, 0, 0))
    draw.ellipse([head_center[0] - 3, head_center[1] - 5, head_center[0] + 3, head_center[1] + 5], fill=(200, 180, 150))
    
    # 脖子
    neck_top = head_center[1] + head_height//2
    neck_bottom = neck_top + 50
    draw.rectangle([head_center[0] - 25, neck_top, head_center[0] + 25, neck_bottom], fill=(255, 220, 177))
    
    # 肩膀和身体
    shoulder_top = neck_bottom
    shoulder_width = 140
    body_height = height - shoulder_top - 20
    draw.rectangle([
        head_center[0] - shoulder_width//2, shoulder_top,
        head_center[0] + shoulder_width//2, shoulder_top + body_height
    ], fill=(100, 150, 200))
    
    # 手臂
    arm_width = 30
    draw.rectangle([
        head_center[0] - shoulder_width//2 - arm_width, shoulder_top + 20,
        head_center[0] - shoulder_width//2, shoulder_top + body_height
    ], fill=(255, 220, 177))
    draw.rectangle([
        head_center[0] + shoulder_width//2, shoulder_top + 20,
        head_center[0] + shoulder_width//2 + arm_width, shoulder_top + body_height
    ], fill=(255, 220, 177))
    
    # 添加标签
    try:
        font = ImageFont.truetype("Arial.ttf", 16)
    except:
        font = ImageFont.load_default()
    
    draw.text((10, 10), "半身人像测试", fill=(0, 0, 0), font=font)
    
    image.save(path, quality=95)
    print(f"   ✅ 半身人像: {path.name}")

def create_head_only_image(path):
    """创建头像测试图片"""
    width, height = 300, 300
    image = Image.new('RGB', (width, height), (240, 240, 240))
    draw = ImageDraw.Draw(image)
    
    # 头部（占大部分画面）
    head_center = (width//2, height//2)
    head_width, head_height = 120, 150
    draw.ellipse([
        head_center[0] - head_width//2, head_center[1] - head_height//2,
        head_center[0] + head_width//2, head_center[1] + head_height//2
    ], fill=(255, 220, 177))
    
    # 五官
    draw.ellipse([head_center[0] - 35, head_center[1] - 30, head_center[0] - 20, head_center[1] - 15], fill=(0, 0, 0))
    draw.ellipse([head_center[0] + 20, head_center[1] - 30, head_center[0] + 35, head_center[1] - 15], fill=(0, 0, 0))
    draw.ellipse([head_center[0] - 5, head_center[1] - 10, head_center[0] + 5, head_center[1] + 5], fill=(200, 180, 150))
    draw.ellipse([head_center[0] - 15, head_center[1] + 20, head_center[0] + 15, head_center[1] + 35], fill=(200, 100, 100))
    
    # 头发
    draw.ellipse([
        head_center[0] - head_width//2, head_center[1] - head_height//2,
        head_center[0] + head_width//2, head_center[1] - head_height//2 + 60
    ], fill=(50, 30, 20))
    
    # 只显示一点点脖子
    neck_top = head_center[1] + head_height//2 - 10
    neck_bottom = height - 10
    draw.rectangle([head_center[0] - 30, neck_top, head_center[0] + 30, neck_bottom], fill=(255, 220, 177))
    
    # 添加标签
    try:
        font = ImageFont.truetype("Arial.ttf", 16)
    except:
        font = ImageFont.load_default()
    
    draw.text((10, 10), "头像测试", fill=(0, 0, 0), font=font)
    
    image.save(path, quality=95)
    print(f"   ✅ 头像: {path.name}")

def run_test():
    """运行测试"""
    print("🚀 智能裁剪处理器 V2 - 一键测试")
    print("=" * 50)
    
    # 创建测试图片
    test_dir = create_test_images()
    
    # 运行处理器
    print(f"\n🔧 开始处理测试图片...")
    
    try:
        script_path = "improved_processor/智能裁剪处理器_v2.py"
        
        if not os.path.exists(script_path):
            print(f"❌ 处理器脚本不存在: {script_path}")
            return False
        
        cmd = [sys.executable, script_path, str(test_dir)]
        print(f"📋 执行命令: {' '.join(cmd)}")
        print("-" * 50)
        
        result = subprocess.run(cmd, capture_output=False, text=True)
        
        print("-" * 50)
        
        if result.returncode == 0:
            print("🎉 测试完成！")
            
            # 显示结果
            output_dir = test_dir / "cropped_output"
            if output_dir.exists():
                output_files = list(output_dir.glob("*"))
                print(f"📂 输出目录: {output_dir}")
                print(f"📊 生成了 {len(output_files)} 个文件:")
                
                for file in sorted(output_files):
                    print(f"   📄 {file.name}")
                
                # 询问是否打开目录
                try:
                    open_dir = input("\n🤔 是否打开输出目录查看结果? (Y/n): ").strip().lower()
                    if open_dir not in ['n', 'no', '否']:
                        if sys.platform == "darwin":  # macOS
                            subprocess.run(["open", str(output_dir)])
                        elif sys.platform == "win32":  # Windows
                            subprocess.run(["explorer", str(output_dir)])
                        else:  # Linux
                            subprocess.run(["xdg-open", str(output_dir)])
                        print("📂 已打开输出目录")
                except KeyboardInterrupt:
                    print("\n👋 测试结束")
            
            return True
        else:
            print(f"❌ 测试失败，返回码: {result.returncode}")
            return False
            
    except Exception as e:
        print(f"❌ 测试出错: {e}")
        return False

if __name__ == "__main__":
    try:
        run_test()
    except KeyboardInterrupt:
        print("\n\n👋 用户取消测试")
    except Exception as e:
        print(f"❌ 测试失败: {e}")
    
    input("\n按回车键退出...")

#!/usr/bin/env python3
"""
智能分层裁剪处理器 - 按优先级单模型处理
流程：RMBG-1.4抠图 → 脖子检测(Haar→MediaPipe→SAM) → 肩膀检测(MediaPipe→SAM) → 胸部检测(MediaPipe→SAM)
每个模型专注单一任务，成功即停止，失败即换模型
"""

import os
import sys
import glob
import threading
import queue
import time
import cv2
import numpy as np
from PIL import Image
import shutil
from datetime import datetime
from pathlib import Path

# 添加抠图工具路径
sys.path.append('../auto_matting_tool')

def clean_path(path):
    """清理路径中的特殊字符"""
    path = path.strip()
    if path.startswith('"') and path.endswith('"'):
        path = path[1:-1]  # 去掉首尾引号
    
    # 处理转义字符
    path = path.replace('\\ ', ' ')  # 处理转义的空格
    path = path.replace('\\(', '(')  # 处理转义的左括号
    path = path.replace('\\)', ')')  # 处理转义的右括号
    return path

class SmartLayeredProcessor:
    def __init__(self):
        self.task_queue = queue.Queue()
        self.processing = False
        self.current_task = None
        self.total_processed = 0
        self.total_failed = 0
        
        # 统计各种成功方式
        self.neck_haar_success = 0
        self.neck_mediapipe_success = 0
        self.neck_sam_success = 0
        self.shoulder_mediapipe_success = 0
        self.shoulder_sam_success = 0
        self.chest_mediapipe_success = 0
        self.chest_sam_success = 0
        
        self.worker_thread = None
        self.running = True
        self.paused = False
        
        # 创建临时目录
        self.temp_dir = "temp_processing"
        os.makedirs(self.temp_dir, exist_ok=True)
        
        # 初始化MediaPipe
        self.init_mediapipe()
        
        # 初始化SAM
        self.init_sam()
        
    def init_mediapipe(self):
        """初始化MediaPipe Pose"""
        try:
            import mediapipe as mp
            self.mp_pose = mp.solutions.pose
            self.pose = self.mp_pose.Pose(
                static_image_mode=True,
                model_complexity=1,
                enable_segmentation=False,
                min_detection_confidence=0.5
            )
            self.mp_drawing = mp.solutions.drawing_utils
            print("✅ MediaPipe Pose 初始化成功")
        except Exception as e:
            print(f"❌ MediaPipe 初始化失败: {e}")
            self.pose = None
    
    def init_sam(self):
        """初始化SAM模型"""
        try:
            from segment_anything import sam_model_registry, SamPredictor
            import torch

            # SAM模型路径
            sam_checkpoint = "../models/sam_vit_b_01ec64.pth"
            model_type = "vit_b"

            # 检查模型文件是否存在
            if not os.path.exists(sam_checkpoint):
                print(f"❌ SAM模型文件不存在: {sam_checkpoint}")
                self.sam_model = None
                self.sam_predictor = None
                return

            # 设置设备
            device = "cuda" if torch.cuda.is_available() else "cpu"

            # 加载SAM模型
            sam = sam_model_registry[model_type](checkpoint=sam_checkpoint)
            sam.to(device=device)

            # 创建预测器
            self.sam_predictor = SamPredictor(sam)
            self.sam_model = sam

            print(f"✅ SAM 模型初始化成功 (设备: {device})")

        except Exception as e:
            print(f"❌ SAM 初始化失败: {e}")
            self.sam_model = None
            self.sam_predictor = None
    
    def start_worker(self):
        """启动工作线程"""
        self.worker_thread = threading.Thread(target=self.worker)
        self.worker_thread.daemon = True
        self.worker_thread.start()
    
    def worker(self):
        """工作线程 - 持续处理队列中的任务"""
        while self.running:
            try:
                # 检查是否暂停
                if self.paused:
                    time.sleep(1)
                    continue

                # 从队列获取任务，超时1秒
                task = self.task_queue.get(timeout=1)
                self.current_task = task
                self.processing = True

                # 处理任务
                if task['type'] == 'single':
                    success, method = self.process_single_image_smart(task['path'])
                    if success:
                        self.total_processed += 1
                        self.update_method_stats(method)
                    else:
                        self.total_failed += 1
                        print(f"❌ 处理失败: {os.path.basename(task['path'])} - {method}")

                elif task['type'] == 'batch':
                    success, method = self.process_batch_smart(task['path'])
                    if not success:
                        print(f"❌ 批量处理失败: {os.path.basename(task['path'])} - {method}")
                else:
                    success, method = False, "未知任务类型"
                    self.total_failed += 1
                    print(f"❌ 处理失败: {os.path.basename(task['path'])} - {method}")

                self.current_task = None
                self.processing = False
                self.task_queue.task_done()

            except queue.Empty:
                continue
            except Exception as e:
                print(f"❌ 处理异常: {e}")
                self.processing = False
                self.current_task = None
                self.task_queue.task_done()
    
    def update_method_stats(self, method):
        """更新方法统计"""
        if "neck_haar" in method:
            self.neck_haar_success += 1
        elif "neck_mediapipe" in method:
            self.neck_mediapipe_success += 1
        elif "neck_sam" in method:
            self.neck_sam_success += 1
        elif "shoulder_mediapipe" in method:
            self.shoulder_mediapipe_success += 1
        elif "shoulder_sam" in method:
            self.shoulder_sam_success += 1
        elif "chest_mediapipe" in method:
            self.chest_mediapipe_success += 1
        elif "chest_sam" in method:
            self.chest_sam_success += 1
    
    def add_task(self, path, task_type):
        """添加任务到队列"""
        task = {'path': path, 'type': task_type}
        self.task_queue.put(task)
        self.show_queue_status()
    
    def show_queue_status(self):
        """显示队列状态"""
        queue_size = self.task_queue.qsize()
        
        # 计算各部位成功总数
        neck_total = self.neck_haar_success + self.neck_mediapipe_success + self.neck_sam_success
        shoulder_total = self.shoulder_mediapipe_success + self.shoulder_sam_success
        chest_total = self.chest_mediapipe_success + self.chest_sam_success

        if self.processing and not self.paused:
            current_name = os.path.basename(self.current_task['path']) if self.current_task else "未知"
            print(f"🔄 正在处理: {current_name} | 队列: {queue_size} | 完成: {self.total_processed}")
            print(f"   脖子: {neck_total} | 肩膀: {shoulder_total} | 胸部: {chest_total} | 失败: {self.total_failed}")
        elif self.paused:
            print(f"⏸️ 队列已暂停 | 队列: {queue_size} | 完成: {self.total_processed}")
            print(f"   脖子: {neck_total} | 肩膀: {shoulder_total} | 胸部: {chest_total} | 失败: {self.total_failed}")
        else:
            if queue_size > 0:
                print(f"⏳ 队列: {queue_size} | 完成: {self.total_processed}")
                print(f"   脖子: {neck_total} | 肩膀: {shoulder_total} | 胸部: {chest_total} | 失败: {self.total_failed}")
            else:
                print(f"✅ 队列空闲 | 完成: {self.total_processed}")
                print(f"   脖子: {neck_total} | 肩膀: {shoulder_total} | 胸部: {chest_total} | 失败: {self.total_failed}")

    def clear_queue(self):
        """清空队列"""
        while not self.task_queue.empty():
            try:
                self.task_queue.get_nowait()
                self.task_queue.task_done()
            except queue.Empty:
                break
        return True

    def pause(self):
        """暂停处理"""
        self.paused = True
        return True

    def resume(self):
        """恢复处理"""
        self.paused = False
        return True
    
    def rmbg_matting(self, image_path, edge_padding=15):
        """使用RMBG-1.4进行抠图，抠图时就保留边缘"""
        try:
            from rembg import remove, new_session
            import cv2
            from io import BytesIO

            # 创建会话
            session = new_session('rmbg-1.4')

            # 读取原图
            with open(image_path, 'rb') as f:
                input_data = f.read()

            # 先用RMBG得到精确的mask
            output_data = remove(input_data, session=session)
            matted_image = Image.open(BytesIO(output_data)).convert('RGBA')

            # 获取RMBG的alpha通道（精确轮廓）
            alpha_array = np.array(matted_image.split()[-1])

            # 对轮廓进行膨胀操作，扩展边缘
            if edge_padding > 0:
                kernel = np.ones((edge_padding * 2 + 1, edge_padding * 2 + 1), np.uint8)
                expanded_alpha = cv2.dilate(alpha_array, kernel, iterations=1)
            else:
                expanded_alpha = alpha_array

            # 用扩展后的轮廓直接抠原图
            original_image = Image.open(image_path).convert('RGBA')
            original_array = np.array(original_image)

            # 应用扩展后的alpha通道
            result_array = original_array.copy()
            result_array[:, :, 3] = expanded_alpha

            result_image = Image.fromarray(result_array)

            # 保存临时文件
            temp_filename = f"temp_{int(time.time())}_{os.path.basename(image_path)}.png"
            temp_path = os.path.join(self.temp_dir, temp_filename)

            result_image.save(temp_path)

            return temp_path

        except Exception as e:
            return None

    # ==================== 脖子检测方法 ====================

    def detect_neck_haar(self, image_path):
        """方法1：使用Haar Cascades检测脖子"""
        try:
            # 读取图像
            pil_image = Image.open(image_path).convert('RGBA')
            img_array = np.array(pil_image)

            # 转换为RGB用于检测
            rgb_array = img_array[:, :, :3]
            gray = cv2.cvtColor(rgb_array, cv2.COLOR_RGB2GRAY)

            # 正面人脸检测
            face_cascade = cv2.CascadeClassifier(cv2.data.haarcascades + 'haarcascade_frontalface_default.xml')
            faces = face_cascade.detectMultiScale(gray, 1.1, 4)

            # 侧面人脸检测
            if len(faces) == 0:
                profile_cascade = cv2.CascadeClassifier(cv2.data.haarcascades + 'haarcascade_profileface.xml')
                faces = profile_cascade.detectMultiScale(gray, 1.1, 4)

            if len(faces) == 0:
                return False, "未检测到人脸"

            # 找到最大的人脸
            largest_face = max(faces, key=lambda face: face[2] * face[3])
            x, y, w, h = largest_face

            # 检查人脸大小是否合理
            image_height, image_width = gray.shape
            face_area_ratio = (w * h) / (image_width * image_height)

            if face_area_ratio < 0.01:
                return False, "检测到的人脸过小"

            if face_area_ratio > 0.8:
                return False, "检测到的人脸过大"

            # 计算脖子位置：人脸底部 + 25%人脸高度
            neck_y = y + h + int(h * 0.25)

            # 确保不超出图像边界
            neck_y = min(neck_y, image_height - 10)

            return True, neck_y

        except Exception as e:
            return False, f"Haar检测出错: {e}"

    def detect_neck_mediapipe(self, image_path):
        """方法2：使用MediaPipe检测脖子"""
        try:
            if self.pose is None:
                return False, "MediaPipe未初始化"

            # 读取图像
            pil_image = Image.open(image_path).convert('RGB')
            img_array = np.array(pil_image)

            # MediaPipe处理
            results = self.pose.process(img_array)

            if not results.pose_landmarks:
                return False, "未检测到人体姿态"

            # 获取关键点
            landmarks = results.pose_landmarks.landmark
            image_height, image_width = img_array.shape[:2]

            # 获取头部相关关键点
            nose = landmarks[0]  # 鼻尖
            left_ear = landmarks[7]  # 左耳
            right_ear = landmarks[8]  # 右耳

            # 检查关键点可见性
            if nose.visibility < 0.5:
                return False, "鼻尖不可见"

            # 计算头部底部位置
            head_bottom_y = max(nose.y, left_ear.y, right_ear.y)

            # 估算脖子位置：头部底部 + 头部高度的20%
            head_top_y = min(nose.y, left_ear.y, right_ear.y)
            head_height = head_bottom_y - head_top_y
            neck_y = head_bottom_y + head_height * 0.2

            # 转换为像素坐标
            neck_y_pixel = int(neck_y * image_height)

            # 确保不超出图像边界
            neck_y_pixel = min(neck_y_pixel, image_height - 10)

            return True, neck_y_pixel

        except Exception as e:
            return False, f"MediaPipe检测出错: {e}"

    def detect_neck_sam(self, image_path):
        """方法3：使用SAM检测脖子"""
        try:
            if self.sam_model is None or self.sam_predictor is None:
                return False, "SAM模型未初始化"

            # 读取图像
            pil_image = Image.open(image_path).convert('RGB')
            img_array = np.array(pil_image)

            # 设置图像到SAM预测器
            self.sam_predictor.set_image(img_array)

            # 获取图像尺寸
            height, width = img_array.shape[:2]

            # 策略1：使用图像上半部分作为提示点来分割头部
            # 在图像上半部分的中心区域设置多个提示点
            input_points = []
            input_labels = []

            # 头部区域的提示点（正样本）
            for y_ratio in [0.15, 0.25, 0.35]:  # 图像高度的15%, 25%, 35%位置
                for x_ratio in [0.4, 0.5, 0.6]:  # 图像宽度的40%, 50%, 60%位置
                    input_points.append([int(width * x_ratio), int(height * y_ratio)])
                    input_labels.append(1)  # 正样本

            # 身体区域的提示点（负样本，避免包含身体）
            for y_ratio in [0.7, 0.8, 0.9]:  # 图像下半部分
                for x_ratio in [0.3, 0.5, 0.7]:
                    input_points.append([int(width * x_ratio), int(height * y_ratio)])
                    input_labels.append(0)  # 负样本

            input_points = np.array(input_points)
            input_labels = np.array(input_labels)

            # 使用SAM进行分割
            masks, scores, logits = self.sam_predictor.predict(
                point_coords=input_points,
                point_labels=input_labels,
                multimask_output=True
            )

            # 选择得分最高的mask
            best_mask_idx = np.argmax(scores)
            best_mask = masks[best_mask_idx]

            # 分析mask找到头部和身体的分界线
            # 找到mask中最下方的像素点
            mask_coords = np.where(best_mask)
            if len(mask_coords[0]) == 0:
                return False, "SAM未检测到有效区域"

            # 获取mask的边界
            min_y, max_y = np.min(mask_coords[0]), np.max(mask_coords[0])
            min_x, max_x = np.min(mask_coords[1]), np.max(mask_coords[1])

            # 分析mask的形状，找到可能的脖子位置
            # 方法：从上到下扫描，找到宽度突然变化的位置
            neck_candidates = []

            for y in range(min_y, max_y, 5):  # 每5像素检查一次
                row_pixels = np.sum(best_mask[y, :])
                if row_pixels > 0:
                    # 计算这一行的宽度
                    row_coords = np.where(best_mask[y, :])[0]
                    if len(row_coords) > 0:
                        row_width = np.max(row_coords) - np.min(row_coords)
                        neck_candidates.append((y, row_width))

            if len(neck_candidates) < 3:
                # 如果检测点太少，使用mask的下边界
                neck_y = max_y + 10
            else:
                # 找到宽度相对较小的位置作为脖子
                # 通常脖子比头部和肩膀都要窄
                widths = [w for y, w in neck_candidates]
                avg_width = np.mean(widths)

                # 找到宽度小于平均宽度70%的位置
                narrow_positions = [(y, w) for y, w in neck_candidates if w < avg_width * 0.7]

                if narrow_positions:
                    # 选择最下方的窄位置
                    neck_y = max(narrow_positions, key=lambda x: x[0])[0] + 15
                else:
                    # 如果没找到明显的窄位置，使用mask下边界的80%位置
                    neck_y = min_y + int((max_y - min_y) * 0.8)

            # 确保脖子位置在合理范围内
            neck_y = max(min_y + 20, min(neck_y, height - 10))

            # 验证位置是否合理
            if neck_y > height * 0.9:
                return False, "SAM检测的脖子位置过低"

            if neck_y < height * 0.1:
                return False, "SAM检测的脖子位置过高"

            return True, neck_y

        except Exception as e:
            return False, f"SAM检测出错: {e}"

    # ==================== 肩膀检测方法 ====================

    def detect_shoulder_mediapipe(self, image_path):
        """方法4：使用MediaPipe检测肩膀"""
        try:
            if self.pose is None:
                return False, "MediaPipe未初始化"

            # 读取图像
            pil_image = Image.open(image_path).convert('RGB')
            img_array = np.array(pil_image)

            # MediaPipe处理
            results = self.pose.process(img_array)

            if not results.pose_landmarks:
                return False, "未检测到人体姿态"

            # 获取关键点
            landmarks = results.pose_landmarks.landmark
            image_height, image_width = img_array.shape[:2]

            # 获取肩膀关键点
            left_shoulder = landmarks[11]  # 左肩膀
            right_shoulder = landmarks[12]  # 右肩膀

            # 检查关键点可见性
            if left_shoulder.visibility < 0.5 and right_shoulder.visibility < 0.5:
                return False, "肩膀关键点不可见"

            # 计算肩膀中线位置
            if left_shoulder.visibility >= 0.5 and right_shoulder.visibility >= 0.5:
                shoulder_y = (left_shoulder.y + right_shoulder.y) / 2
                shoulder_width = abs(left_shoulder.x - right_shoulder.x)
            elif left_shoulder.visibility >= 0.5:
                shoulder_y = left_shoulder.y
                shoulder_width = 0.1  # 默认宽度
            else:
                shoulder_y = right_shoulder.y
                shoulder_width = 0.1  # 默认宽度

            # 肩膀裁剪位置：肩膀位置 + 肩膀宽度的12%
            crop_y = shoulder_y + shoulder_width * 0.12

            # 转换为像素坐标
            crop_y_pixel = int(crop_y * image_height)

            # 确保不超出图像边界
            crop_y_pixel = min(crop_y_pixel, image_height - 10)

            return True, crop_y_pixel

        except Exception as e:
            return False, f"MediaPipe肩膀检测出错: {e}"

    def detect_shoulder_sam(self, image_path):
        """方法5：使用SAM检测肩膀"""
        try:
            if self.sam_model is None or self.sam_predictor is None:
                return False, "SAM模型未初始化"

            # 读取图像
            pil_image = Image.open(image_path).convert('RGB')
            img_array = np.array(pil_image)

            # 设置图像到SAM预测器
            self.sam_predictor.set_image(img_array)

            # 获取图像尺寸
            height, width = img_array.shape[:2]

            # 策略：使用肩膀区域的提示点来分割上半身
            input_points = []
            input_labels = []

            # 肩膀区域的提示点（正样本）
            # 肩膀通常在图像的30%-60%高度位置
            for y_ratio in [0.3, 0.4, 0.5, 0.6]:
                for x_ratio in [0.2, 0.3, 0.7, 0.8]:  # 左右肩膀位置
                    input_points.append([int(width * x_ratio), int(height * y_ratio)])
                    input_labels.append(1)  # 正样本

            # 头部区域（负样本，避免包含头部）
            for y_ratio in [0.1, 0.2]:
                for x_ratio in [0.4, 0.5, 0.6]:
                    input_points.append([int(width * x_ratio), int(height * y_ratio)])
                    input_labels.append(0)  # 负样本

            # 下半身区域（负样本）
            for y_ratio in [0.8, 0.9]:
                for x_ratio in [0.3, 0.5, 0.7]:
                    input_points.append([int(width * x_ratio), int(height * y_ratio)])
                    input_labels.append(0)  # 负样本

            input_points = np.array(input_points)
            input_labels = np.array(input_labels)

            # 使用SAM进行分割
            masks, scores, logits = self.sam_predictor.predict(
                point_coords=input_points,
                point_labels=input_labels,
                multimask_output=True
            )

            # 选择得分最高的mask
            best_mask_idx = np.argmax(scores)
            best_mask = masks[best_mask_idx]

            # 分析mask找到肩膀的下边界
            mask_coords = np.where(best_mask)
            if len(mask_coords[0]) == 0:
                return False, "SAM未检测到肩膀区域"

            # 获取mask的边界
            min_y, max_y = np.min(mask_coords[0]), np.max(mask_coords[0])

            # 分析肩膀宽度变化，找到肩膀下边界
            shoulder_candidates = []

            # 从肩膀区域开始向下扫描
            start_y = int(height * 0.3)  # 从30%高度开始
            end_y = min(int(height * 0.7), max_y)  # 到70%高度或mask边界

            max_width = 0
            for y in range(start_y, end_y, 3):
                if y < height:
                    row_pixels = np.sum(best_mask[y, :])
                    if row_pixels > 0:
                        row_coords = np.where(best_mask[y, :])[0]
                        if len(row_coords) > 0:
                            row_width = np.max(row_coords) - np.min(row_coords)
                            max_width = max(max_width, row_width)
                            shoulder_candidates.append((y, row_width))

            if len(shoulder_candidates) < 3:
                # 如果检测点太少，使用mask下边界的位置
                shoulder_y = max_y + 20
            else:
                # 找到宽度达到最大宽度80%以上的最下方位置
                wide_positions = [(y, w) for y, w in shoulder_candidates if w >= max_width * 0.8]

                if wide_positions:
                    # 选择最下方的宽位置，再向下偏移一点
                    shoulder_y = max(wide_positions, key=lambda x: x[0])[0] + 25
                else:
                    # 使用mask的下边界
                    shoulder_y = max_y + 15

            # 确保肩膀位置在合理范围内
            shoulder_y = max(int(height * 0.2), min(shoulder_y, height - 10))

            # 验证位置是否合理
            if shoulder_y > height * 0.85:
                return False, "SAM检测的肩膀位置过低"

            if shoulder_y < height * 0.15:
                return False, "SAM检测的肩膀位置过高"

            return True, shoulder_y

        except Exception as e:
            return False, f"SAM肩膀检测出错: {e}"

    # ==================== 胸部检测方法 ====================

    def detect_chest_mediapipe(self, image_path):
        """方法6：使用MediaPipe检测胸部"""
        try:
            if self.pose is None:
                return False, "MediaPipe未初始化"

            # 读取图像
            pil_image = Image.open(image_path).convert('RGB')
            img_array = np.array(pil_image)

            # MediaPipe处理
            results = self.pose.process(img_array)

            if not results.pose_landmarks:
                return False, "未检测到人体姿态"

            # 获取关键点
            landmarks = results.pose_landmarks.landmark
            image_height, image_width = img_array.shape[:2]

            # 获取肩膀和髋部关键点
            left_shoulder = landmarks[11]  # 左肩膀
            right_shoulder = landmarks[12]  # 右肩膀
            left_hip = landmarks[23]  # 左髋部
            right_hip = landmarks[24]  # 右髋部

            # 检查关键点可见性
            shoulder_visible = left_shoulder.visibility >= 0.5 or right_shoulder.visibility >= 0.5
            hip_visible = left_hip.visibility >= 0.5 or right_hip.visibility >= 0.5

            if not shoulder_visible:
                return False, "肩膀关键点不可见"

            # 计算肩膀位置
            if left_shoulder.visibility >= 0.5 and right_shoulder.visibility >= 0.5:
                shoulder_y = (left_shoulder.y + right_shoulder.y) / 2
            elif left_shoulder.visibility >= 0.5:
                shoulder_y = left_shoulder.y
            else:
                shoulder_y = right_shoulder.y

            # 计算胸部位置
            if hip_visible:
                # 如果髋部可见，胸部位置在肩膀和髋部之间的35%处
                if left_hip.visibility >= 0.5 and right_hip.visibility >= 0.5:
                    hip_y = (left_hip.y + right_hip.y) / 2
                elif left_hip.visibility >= 0.5:
                    hip_y = left_hip.y
                else:
                    hip_y = right_hip.y

                chest_y = shoulder_y + (hip_y - shoulder_y) * 0.35
            else:
                # 如果髋部不可见，估算胸部位置
                chest_y = shoulder_y + 0.15  # 肩膀下方15%图像高度

            # 转换为像素坐标
            chest_y_pixel = int(chest_y * image_height)

            # 确保不超出图像边界
            chest_y_pixel = min(chest_y_pixel, image_height - 10)

            return True, chest_y_pixel

        except Exception as e:
            return False, f"MediaPipe胸部检测出错: {e}"

    def detect_chest_sam(self, image_path):
        """方法7：使用SAM检测胸部"""
        try:
            if self.sam_model is None or self.sam_predictor is None:
                return False, "SAM模型未初始化"

            # 读取图像
            pil_image = Image.open(image_path).convert('RGB')
            img_array = np.array(pil_image)

            # 设置图像到SAM预测器
            self.sam_predictor.set_image(img_array)

            # 获取图像尺寸
            height, width = img_array.shape[:2]

            # 策略：使用胸部区域的提示点来分割躯干
            input_points = []
            input_labels = []

            # 胸部区域的提示点（正样本）
            # 胸部通常在图像的40%-75%高度位置
            for y_ratio in [0.4, 0.5, 0.6, 0.7]:
                for x_ratio in [0.3, 0.4, 0.5, 0.6, 0.7]:  # 胸部中央区域
                    input_points.append([int(width * x_ratio), int(height * y_ratio)])
                    input_labels.append(1)  # 正样本

            # 头部区域（负样本）
            for y_ratio in [0.1, 0.2, 0.3]:
                for x_ratio in [0.4, 0.5, 0.6]:
                    input_points.append([int(width * x_ratio), int(height * y_ratio)])
                    input_labels.append(0)  # 负样本

            # 下半身区域（负样本）
            for y_ratio in [0.85, 0.9, 0.95]:
                for x_ratio in [0.3, 0.5, 0.7]:
                    input_points.append([int(width * x_ratio), int(height * y_ratio)])
                    input_labels.append(0)  # 负样本

            input_points = np.array(input_points)
            input_labels = np.array(input_labels)

            # 使用SAM进行分割
            masks, scores, logits = self.sam_predictor.predict(
                point_coords=input_points,
                point_labels=input_labels,
                multimask_output=True
            )

            # 选择得分最高的mask
            best_mask_idx = np.argmax(scores)
            best_mask = masks[best_mask_idx]

            # 分析mask找到胸部的合适裁剪位置
            mask_coords = np.where(best_mask)
            if len(mask_coords[0]) == 0:
                return False, "SAM未检测到胸部区域"

            # 获取mask的边界
            min_y, max_y = np.min(mask_coords[0]), np.max(mask_coords[0])

            # 分析躯干形状，找到胸部的合适裁剪位置
            chest_candidates = []

            # 从胸部区域开始向下扫描
            start_y = int(height * 0.4)  # 从40%高度开始
            end_y = min(int(height * 0.8), max_y)  # 到80%高度或mask边界

            for y in range(start_y, end_y, 5):
                if y < height:
                    row_pixels = np.sum(best_mask[y, :])
                    if row_pixels > 0:
                        row_coords = np.where(best_mask[y, :])[0]
                        if len(row_coords) > 0:
                            row_width = np.max(row_coords) - np.min(row_coords)
                            chest_candidates.append((y, row_width))

            if len(chest_candidates) < 3:
                # 如果检测点太少，使用mask中部位置
                chest_y = min_y + int((max_y - min_y) * 0.6)
            else:
                # 找到一个合适的胸部裁剪位置
                # 通常选择躯干宽度稳定的区域的下方
                widths = [w for y, w in chest_candidates]
                avg_width = np.mean(widths)

                # 找到宽度接近平均宽度的位置（稳定的躯干区域）
                stable_positions = [(y, w) for y, w in chest_candidates
                                  if abs(w - avg_width) < avg_width * 0.2]

                if stable_positions:
                    # 选择稳定区域的中下部位置
                    stable_y_coords = [y for y, w in stable_positions]
                    chest_y = int(np.percentile(stable_y_coords, 70)) + 20  # 70%分位数位置
                else:
                    # 使用mask的60%位置
                    chest_y = min_y + int((max_y - min_y) * 0.6)

            # 确保胸部位置在合理范围内
            chest_y = max(int(height * 0.3), min(chest_y, height - 10))

            # 验证位置是否合理
            if chest_y > height * 0.9:
                return False, "SAM检测的胸部位置过低"

            if chest_y < height * 0.25:
                return False, "SAM检测的胸部位置过高"

            return True, chest_y

        except Exception as e:
            return False, f"SAM胸部检测出错: {e}"

    # ==================== 核心智能处理逻辑 ====================

    def process_single_image_smart(self, image_path, batch_output_dir=None):
        """智能分层处理单张图片"""
        try:
            # 步骤1：RMBG-1.4抠图
            matted_path = self.rmbg_matting(image_path)
            if not matted_path:
                return False, "抠图失败"

            # 按优先级尝试各种检测方法
            detection_methods = [
                # 脖子检测（最优先）
                ("neck_haar", self.detect_neck_haar, "脖子"),
                ("neck_mediapipe", self.detect_neck_mediapipe, "脖子"),
                ("neck_sam", self.detect_neck_sam, "脖子"),

                # 肩膀检测（次优先）
                ("shoulder_mediapipe", self.detect_shoulder_mediapipe, "肩膀"),
                ("shoulder_sam", self.detect_shoulder_sam, "肩膀"),

                # 胸部检测（最后尝试）
                ("chest_mediapipe", self.detect_chest_mediapipe, "胸部"),
                ("chest_sam", self.detect_chest_sam, "胸部"),
            ]

            # 逐个尝试检测方法
            for method_name, method_func, body_part in detection_methods:
                success, result = method_func(matted_path)

                if success:
                    # 检测成功，进行裁剪
                    crop_y = result
                    base_output_path = self.get_output_base_path(image_path, f"{method_name}_cropped", batch_output_dir)
                    filename = f"{Path(image_path).stem}_{method_name}_cropped"
                    is_batch = batch_output_dir is not None

                    if self.crop_and_save(matted_path, crop_y, base_output_path, filename, is_batch):
                        # 清理临时文件
                        os.remove(matted_path)
                        return True, method_name
                    else:
                        # 裁剪失败，继续尝试下一个方法
                        continue
                else:
                    # 检测失败，继续尝试下一个方法
                    continue

            # 所有方法都失败了
            # 清理临时文件
            os.remove(matted_path)

            # 记录失败原因
            failure_reason = "所有检测方法都失败"
            self.log_failure(image_path, failure_reason)

            return False, failure_reason

        except Exception as e:
            return False, f"处理异常: {e}"

    def crop_and_save(self, image_path, crop_y, base_output_path, filename, is_batch=False):
        """裁剪图像并保存PNG和JPG两个版本"""
        try:
            # 读取图像
            pil_image = Image.open(image_path).convert('RGBA')
            img_array = np.array(pil_image)

            # 裁剪
            cropped_array = img_array[:crop_y, :, :]
            cropped_image = Image.fromarray(cropped_array)

            if is_batch:
                # 批量处理：创建png和jpg子文件夹
                png_dir = os.path.join(base_output_path, "png")
                jpg_dir = os.path.join(base_output_path, "jpg")
                os.makedirs(png_dir, exist_ok=True)
                os.makedirs(jpg_dir, exist_ok=True)

                png_path = os.path.join(png_dir, f"{filename}.png")
                jpg_path = os.path.join(jpg_dir, f"{filename}.jpg")
            else:
                # 单文件处理：直接保存在指定目录
                png_path = os.path.join(base_output_path, f"{filename}.png")
                jpg_path = os.path.join(base_output_path, f"{filename}.jpg")

            # 保存PNG版本（透明背景）
            cropped_image.save(png_path)

            # 保存JPG版本（白色背景）
            jpg_image = Image.new('RGB', cropped_image.size, (255, 255, 255))
            jpg_image.paste(cropped_image, mask=cropped_image.split()[-1])
            jpg_image.save(jpg_path, quality=95)

            return True

        except Exception as e:
            return False

    def get_output_base_path(self, input_path, suffix="processed", batch_output_dir=None):
        """生成输出基础路径"""
        input_file = Path(input_path)

        if batch_output_dir:
            # 批量处理：返回批量输出目录
            return str(batch_output_dir)
        else:
            # 单文件处理：返回原图片同目录
            return str(input_file.parent)

    def log_failure(self, image_path, reason):
        """记录失败原因到全局日志"""
        log_file = "processing_log.txt"  # 保存在当前工作目录

        with open(log_file, "a", encoding="utf-8") as f:
            timestamp = datetime.now().strftime("%Y-%m-%d %H:%M:%S")
            filename = os.path.basename(image_path)
            f.write(f"[{timestamp}] {filename} - {reason}\n")

    def process_batch_smart(self, input_path):
        """智能批量处理"""
        try:
            # 检查目录是否存在
            if not os.path.exists(input_path):
                return False, "目录不存在"

            # 创建批量输出目录
            input_dir = Path(input_path)
            batch_output_dir = f"{input_path}_processed"
            os.makedirs(batch_output_dir, exist_ok=True)

            # 获取所有图片文件
            supported_formats = ['*.jpg', '*.jpeg', '*.png', '*.bmp', '*.tiff', '*.webp',
                                '*.JPG', '*.JPEG', '*.PNG', '*.BMP', '*.TIFF', '*.WEBP']
            image_files = []
            for pattern in supported_formats:
                image_files.extend(glob.glob(os.path.join(input_path, pattern)))

            if not image_files:
                return False, "未找到图片文件"

            # 逐个处理图片
            success_count = 0
            failed_count = 0
            for image_file in image_files:
                success, method = self.process_single_image_smart(image_file, batch_output_dir)
                if success:
                    success_count += 1
                    self.total_processed += 1
                    self.update_method_stats(method)
                else:
                    failed_count += 1
                    self.total_failed += 1

            return True, f"批量处理完成: {success_count}/{len(image_files)} → {batch_output_dir}"

        except Exception as e:
            return False, f"批量处理异常: {e}"

# ==================== 交互功能 ====================

def handle_command(command, processor):
    """处理快捷命令"""
    parts = command.lower().strip().split()
    cmd = parts[0] if parts else ""

    # 基础命令
    if cmd in ['status', 'st']:
        processor.show_queue_status()
        return True
    elif cmd in ['help', 'h', '?']:
        show_help()
        return True
    elif cmd in ['quit', 'exit', 'q']:
        processor.running = False
        print("👋 再见!")
        sys.exit(0)
    elif cmd == 'clear' and len(parts) == 1:
        return clear_queue(processor)
    elif cmd == 'pause':
        return pause_queue(processor)
    elif cmd == 'resume':
        return resume_queue(processor)
    elif cmd == 'open':
        return open_results()
    elif cmd == 'stats':
        return show_detailed_stats(processor)
    else:
        return False

def show_help():
    """显示帮助信息"""
    print("\n📖 智能分层裁剪处理器 - 快捷命令:")
    print("=" * 70)
    print("📈 status/st         - 显示队列状态")
    print("📊 stats             - 显示详细统计信息")
    print("❓ help/h/?          - 显示此帮助")
    print("🚪 quit/exit/q       - 退出程序")
    print()
    print("🎮 队列控制:")
    print("⏸️ pause             - 暂停队列处理")
    print("▶️ resume            - 恢复队列处理")
    print("🗑️ clear             - 清空队列")
    print("📁 open              - 打开当前目录")
    print()
    print("🎯 智能分层处理流程:")
    print("1. RMBG-1.4抠图（去除背景，15像素边缘保留）")
    print("2. 脖子检测：Haar → MediaPipe → SAM")
    print("3. 肩膀检测：MediaPipe → SAM")
    print("4. 胸部检测：MediaPipe → SAM")
    print("5. 生成两种格式：PNG透明背景 + JPG白色背景")
    print()
    print("📁 输出结构:")
    print("单文件：保存在原图片同目录")
    print("批量处理：创建'_processed'文件夹/png/jpg/")
    print("=" * 70)
    print("💡 直接拖拽图片/文件夹进行处理，可无限添加到队列")

def show_detailed_stats(processor):
    """显示详细统计信息"""
    print("\n📊 详细统计信息:")
    print("=" * 50)
    print(f"总处理数: {processor.total_processed}")
    print(f"失败数: {processor.total_failed}")
    print()
    print("🎯 脖子检测成功:")
    print(f"  Haar Cascades: {processor.neck_haar_success}")
    print(f"  MediaPipe: {processor.neck_mediapipe_success}")
    print(f"  SAM: {processor.neck_sam_success}")
    print()
    print("👐 肩膀检测成功:")
    print(f"  MediaPipe: {processor.shoulder_mediapipe_success}")
    print(f"  SAM: {processor.shoulder_sam_success}")
    print()
    print("🫁 胸部检测成功:")
    print(f"  MediaPipe: {processor.chest_mediapipe_success}")
    print(f"  SAM: {processor.chest_sam_success}")
    print("=" * 50)
    return True

def clear_queue(processor):
    """清空队列"""
    if processor.clear_queue():
        print("✅ 队列已清空")
        processor.show_queue_status()
    else:
        print("❌ 清空队列失败")
    return True

def pause_queue(processor):
    """暂停队列"""
    if processor.pause():
        print("⏸️ 队列已暂停")
        processor.show_queue_status()
    else:
        print("❌ 暂停失败")
    return True

def resume_queue(processor):
    """恢复队列"""
    if processor.resume():
        print("▶️ 队列已恢复")
        processor.show_queue_status()
    else:
        print("❌ 恢复失败")
    return True

def open_results():
    """打开当前工作目录"""
    try:
        os.system("open .")
        print("📁 已打开当前目录")
    except:
        print("❌ 打开文件夹失败")
    return True

def main():
    print("🎯 智能分层裁剪处理器")
    print("流程：RMBG-1.4抠图 → 脖子(Haar→MediaPipe→SAM) → 肩膀(MediaPipe→SAM) → 胸部(MediaPipe→SAM)")
    print("=" * 80)
    print("💡 使用说明:")
    print("  • 单模型单任务，失败即换模型")
    print("  • 优先级：脖子 > 肩膀 > 胸部")
    print("  • 每个结果生成PNG(透明)和JPG(白底)两个版本")
    print("  • 单文件：直接保存在原图片同目录")
    print("  • 批量处理：创建'_processed'文件夹/png/jpg/")
    print("  • 只有失败时才会通知，成功时静默处理")
    print("  • 输入 'status' 查看队列状态，'stats' 查看详细统计")
    print("  • 输入 'help' 查看更多命令")
    print("  • 按 Ctrl+C 或输入 'quit' 退出")
    print("=" * 80)
    print()

    # 创建处理器并启动工作线程
    processor = SmartLayeredProcessor()
    processor.start_worker()

    try:
        while True:
            print("📎 拖拽文件/文件夹，或输入命令:")
            user_input = input(">>> ").strip()

            if not user_input:
                processor.show_queue_status()
                continue

            # 检查是否是快捷命令
            if handle_command(user_input, processor):
                continue

            # 处理为文件路径
            input_path = clean_path(user_input)

            # 判断是文件还是目录
            if os.path.isfile(input_path):
                processor.add_task(input_path, 'single')
            elif os.path.isdir(input_path):
                processor.add_task(input_path, 'batch')
            else:
                print("❌ 路径不存在或不是有效的文件/文件夹")
                continue

    except KeyboardInterrupt:
        processor.running = False
        print("\n👋 再见!")
        sys.exit(0)
    except Exception as e:
        print(f"❌ 发生错误: {e}")
        processor.running = False

if __name__ == "__main__":
    main()

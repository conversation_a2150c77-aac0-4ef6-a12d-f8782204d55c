#!/usr/bin/env python3
"""
智能裁剪处理器 V2 - SAM主导版本
流程：MediaPipe分析身体结构 → SAM精确分割 → Haar兜底
专注批量自动处理，无置信度显示
"""

import os
import sys
import cv2
import numpy as np
from PIL import Image
from pathlib import Path
import shutil

# 添加抠图工具路径
sys.path.append('../auto_matting_tool')

def clean_path(path):
    """清理路径中的特殊字符"""
    path = path.strip()
    if path.startswith('"') and path.endswith('"'):
        path = path[1:-1]
    return path.replace('\\', '/')

class SmartCropperV2:
    def __init__(self):
        """初始化所有模型"""
        self.pose = None
        self.sam_model = None
        self.sam_predictor = None
        
        # 初始化MediaPipe
        self.init_mediapipe()
        
        # 初始化SAM
        self.init_sam()
        
        print("🚀 智能裁剪处理器 V2 初始化完成")
    
    def init_mediapipe(self):
        """初始化MediaPipe"""
        try:
            import mediapipe as mp
            self.mp_pose = mp.solutions.pose
            self.pose = self.mp_pose.Pose(
                static_image_mode=True,
                model_complexity=1,
                enable_segmentation=False,
                min_detection_confidence=0.1  # 进一步降低阈值
            )
            print("✅ MediaPipe 初始化成功")
        except Exception as e:
            print(f"❌ MediaPipe 初始化失败: {e}")
            self.pose = None
    
    def init_sam(self):
        """初始化SAM模型"""
        try:
            from segment_anything import sam_model_registry, SamPredictor
            import torch
            
            # SAM模型路径
            current_dir = os.path.dirname(os.path.abspath(__file__))
            parent_dir = os.path.dirname(current_dir)
            sam_checkpoint = os.path.join(parent_dir, "models", "sam_vit_b_01ec64.pth")
            model_type = "vit_b"
            
            if not os.path.exists(sam_checkpoint):
                print(f"❌ SAM模型文件不存在: {sam_checkpoint}")
                return
            
            device = "cuda" if torch.cuda.is_available() else "cpu"
            sam = sam_model_registry[model_type](checkpoint=sam_checkpoint)
            sam.to(device=device)
            
            self.sam_predictor = SamPredictor(sam)
            self.sam_model = sam
            print(f"✅ SAM 模型初始化成功 (设备: {device})")
            
        except Exception as e:
            print(f"❌ SAM 初始化失败: {e}")
            self.sam_model = None
            self.sam_predictor = None
    
    def rmbg_matting(self, image_path):
        """RMBG-1.4抠图"""
        try:
            from rmbg import remove_background
            
            # 生成输出路径
            input_path = Path(image_path)
            temp_dir = input_path.parent / "temp_matted"
            temp_dir.mkdir(exist_ok=True)
            output_path = temp_dir / f"{input_path.stem}_matted.png"
            
            # 执行抠图
            success = remove_background(str(image_path), str(output_path))
            
            if success and output_path.exists():
                return str(output_path)
            else:
                return None
                
        except Exception as e:
            print(f"抠图失败: {e}")
            return None
    
    def analyze_body_structure(self, image_path):
        """使用MediaPipe分析身体结构"""
        if self.pose is None:
            return None
        
        try:
            # 读取图像
            pil_image = Image.open(image_path).convert('RGB')
            img_array = np.array(pil_image)
            
            # MediaPipe处理
            results = self.pose.process(img_array)

            if not results.pose_landmarks:
                print("MediaPipe未检测到人体姿态")
                return None

            print("MediaPipe检测到人体姿态")
            
            landmarks = results.pose_landmarks.landmark
            height, width = img_array.shape[:2]
            
            # 分析身体结构
            structure = {
                'image_type': 'unknown',
                'head_points': [],
                'shoulder_points': [],
                'body_points': [],
                'crop_strategy': 'haar_fallback'
            }
            
            # 检查关键点可见性
            nose = landmarks[0]
            left_shoulder = landmarks[11]
            right_shoulder = landmarks[12]
            left_hip = landmarks[23]
            right_hip = landmarks[24]
            
            # 判断图片类型
            head_visible = nose.visibility > 0.3
            shoulders_visible = (left_shoulder.visibility > 0.3 or right_shoulder.visibility > 0.3)
            hips_visible = (left_hip.visibility > 0.3 or right_hip.visibility > 0.3)

            print(f"可见性检查: 头部={head_visible}({nose.visibility:.3f}), 肩膀={shoulders_visible}({left_shoulder.visibility:.3f},{right_shoulder.visibility:.3f}), 髋部={hips_visible}")
            
            if head_visible and shoulders_visible:
                if hips_visible:
                    structure['image_type'] = 'full_body'
                else:
                    structure['image_type'] = 'half_body'
                
                # 收集关键点
                structure['head_points'] = [
                    (int(nose.x * width), int(nose.y * height)),
                    (int(landmarks[7].x * width), int(landmarks[7].y * height)),  # 左耳
                    (int(landmarks[8].x * width), int(landmarks[8].y * height))   # 右耳
                ]
                
                structure['shoulder_points'] = []
                if left_shoulder.visibility > 0.3:
                    structure['shoulder_points'].append((int(left_shoulder.x * width), int(left_shoulder.y * height)))
                if right_shoulder.visibility > 0.3:
                    structure['shoulder_points'].append((int(right_shoulder.x * width), int(right_shoulder.y * height)))
                
                structure['crop_strategy'] = 'sam_guided'
                
            elif head_visible:
                structure['image_type'] = 'head_only'
                structure['crop_strategy'] = 'haar_face'
            
            return structure
            
        except Exception as e:
            return None
    
    def sam_guided_crop(self, image_path, body_structure):
        """SAM主导的精确分割裁剪"""
        if self.sam_model is None or self.sam_predictor is None:
            return None
        
        try:
            # 读取图像
            pil_image = Image.open(image_path).convert('RGB')
            img_array = np.array(pil_image)
            
            # 设置图像到SAM
            self.sam_predictor.set_image(img_array)
            
            height, width = img_array.shape[:2]
            
            # 基于MediaPipe分析结果设置提示点
            input_points = []
            input_labels = []
            
            # 头部正样本点
            for point in body_structure['head_points']:
                input_points.append(point)
                input_labels.append(1)
            
            # 肩膀负样本点（避免包含肩膀）
            for point in body_structure['shoulder_points']:
                # 在肩膀下方设置负样本
                neg_point = (point[0], min(point[1] + 30, height - 1))
                input_points.append(neg_point)
                input_labels.append(0)
            
            # 添加一些辅助点
            # 头部区域的额外正样本
            head_center_x = sum(p[0] for p in body_structure['head_points']) // len(body_structure['head_points'])
            head_center_y = sum(p[1] for p in body_structure['head_points']) // len(body_structure['head_points'])
            
            for offset_y in [-20, 0, 20]:
                for offset_x in [-15, 0, 15]:
                    x = max(0, min(width - 1, head_center_x + offset_x))
                    y = max(0, min(height - 1, head_center_y + offset_y))
                    input_points.append((x, y))
                    input_labels.append(1)
            
            # 图片边缘负样本
            for x in [10, width - 10]:
                for y in [10, height - 10]:
                    input_points.append((x, y))
                    input_labels.append(0)
            
            input_points = np.array(input_points)
            input_labels = np.array(input_labels)
            
            # SAM分割
            masks, scores, logits = self.sam_predictor.predict(
                point_coords=input_points,
                point_labels=input_labels,
                multimask_output=True
            )
            
            # 选择最佳mask
            best_mask_idx = np.argmax(scores)
            best_mask = masks[best_mask_idx]
            
            # 分析mask找到裁剪位置
            mask_coords = np.where(best_mask)
            if len(mask_coords[0]) == 0:
                return None
            
            # 找到头部区域的下边界
            max_y = np.max(mask_coords[0])
            
            # 在头部下边界基础上稍微向下偏移作为裁剪线
            crop_y = min(max_y + 20, height - 10)
            
            return crop_y
            
        except Exception as e:
            return None
    
    def haar_fallback_crop(self, image_path):
        """Haar兜底裁剪"""
        try:
            # 读取图像
            pil_image = Image.open(image_path).convert('RGBA')
            img_array = np.array(pil_image)
            rgb_array = img_array[:, :, :3]
            gray = cv2.cvtColor(rgb_array, cv2.COLOR_RGB2GRAY)
            
            # 人脸检测
            face_cascade = cv2.CascadeClassifier(cv2.data.haarcascades + 'haarcascade_frontalface_default.xml')
            faces = face_cascade.detectMultiScale(gray, 1.1, 4)
            
            if len(faces) == 0:
                # 尝试侧脸检测
                profile_cascade = cv2.CascadeClassifier(cv2.data.haarcascades + 'haarcascade_profileface.xml')
                faces = profile_cascade.detectMultiScale(gray, 1.1, 4)
            
            if len(faces) == 0:
                return None
            
            # 找最大人脸
            largest_face = max(faces, key=lambda face: face[2] * face[3])
            x, y, w, h = largest_face
            
            # 计算裁剪位置：人脸底部 + 30%人脸高度
            crop_y = y + h + int(h * 0.3)
            crop_y = min(crop_y, gray.shape[0] - 10)
            
            return crop_y
            
        except Exception as e:
            return None

    def crop_and_save(self, image_path, crop_y, output_dir, filename):
        """裁剪并保存图片"""
        try:
            # 读取图像
            pil_image = Image.open(image_path).convert('RGBA')

            # 裁剪
            width, height = pil_image.size
            cropped_image = pil_image.crop((0, 0, width, crop_y))

            # 确保输出目录存在
            os.makedirs(output_dir, exist_ok=True)

            # 保存PNG版本（保持透明度）
            png_path = os.path.join(output_dir, f"{filename}.png")
            cropped_image.save(png_path)

            # 保存JPG版本（白色背景）
            jpg_path = os.path.join(output_dir, f"{filename}.jpg")
            jpg_image = Image.new('RGB', cropped_image.size, (255, 255, 255))
            jpg_image.paste(cropped_image, mask=cropped_image.split()[-1])
            jpg_image.save(jpg_path, quality=95)

            return True

        except Exception as e:
            print(f"保存失败: {e}")
            return False

    def process_single_image(self, image_path, output_dir=None):
        """处理单张图片"""
        try:
            print(f"处理: {Path(image_path).name}")

            # 步骤1：抠图（临时跳过用于测试）
            matted_path = self.rmbg_matting(image_path)
            if not matted_path:
                print(f"⚠️  抠图跳过，直接使用原图测试")
                matted_path = image_path

            # 步骤2：分析身体结构
            body_structure = self.analyze_body_structure(matted_path)
            print(f"身体结构分析: {body_structure}")

            crop_y = None
            method_used = "未知"

            # 步骤3：根据分析结果选择裁剪策略
            if body_structure and body_structure['crop_strategy'] == 'sam_guided':
                print("尝试SAM精确分割...")
                # SAM主导裁剪
                crop_y = self.sam_guided_crop(matted_path, body_structure)
                method_used = "SAM精确分割"
                print(f"SAM结果: {crop_y}")

            if crop_y is None:
                print("尝试Haar兜底...")
                # Haar兜底
                crop_y = self.haar_fallback_crop(matted_path)
                method_used = "Haar人脸检测"
                print(f"Haar结果: {crop_y}")

            if crop_y is None:
                print(f"❌ 所有检测方法都失败")
                # 只有当matted_path不是原图时才删除
                if matted_path != image_path:
                    os.remove(matted_path)
                return False

            # 步骤4：裁剪保存
            if output_dir is None:
                output_dir = Path(image_path).parent / "cropped_output"

            filename = f"{Path(image_path).stem}_cropped"

            if self.crop_and_save(matted_path, crop_y, output_dir, filename):
                print(f"✅ 成功 - {method_used}")
                # 只有当matted_path不是原图时才删除
                if matted_path != image_path:
                    os.remove(matted_path)
                return True
            else:
                print(f"❌ 保存失败")
                # 只有当matted_path不是原图时才删除
                if matted_path != image_path:
                    os.remove(matted_path)
                return False

        except Exception as e:
            print(f"❌ 处理出错: {e}")
            return False

    def process_batch(self, input_dir, output_dir=None):
        """批量处理"""
        input_path = Path(input_dir)
        if not input_path.exists():
            print(f"❌ 输入目录不存在: {input_dir}")
            return

        # 支持的图片格式
        image_extensions = {'.jpg', '.jpeg', '.png', '.bmp', '.tiff', '.webp'}

        # 找到所有图片文件
        image_files = []
        for ext in image_extensions:
            image_files.extend(input_path.glob(f"*{ext}"))
            image_files.extend(input_path.glob(f"*{ext.upper()}"))

        if not image_files:
            print(f"❌ 在 {input_dir} 中未找到图片文件")
            return

        print(f"🚀 开始批量处理 {len(image_files)} 张图片")

        success_count = 0
        for i, image_file in enumerate(image_files, 1):
            print(f"\n[{i}/{len(image_files)}] ", end="")

            if self.process_single_image(str(image_file), output_dir):
                success_count += 1

        print(f"\n🎉 批量处理完成！成功: {success_count}/{len(image_files)}")

def main():
    """主函数"""
    print("智能裁剪处理器 V2 - SAM主导版本")
    print("=" * 50)

    if len(sys.argv) < 2:
        print("用法:")
        print("  python 智能裁剪处理器_v2.py <图片路径或目录>")
        print("  python 智能裁剪处理器_v2.py <图片路径或目录> <输出目录>")
        return

    input_path = clean_path(sys.argv[1])
    output_dir = clean_path(sys.argv[2]) if len(sys.argv) > 2 else None

    # 初始化处理器
    processor = SmartCropperV2()

    # 判断是单文件还是目录
    if os.path.isfile(input_path):
        processor.process_single_image(input_path, output_dir)
    elif os.path.isdir(input_path):
        processor.process_batch(input_path, output_dir)
    else:
        print(f"❌ 路径不存在: {input_path}")

if __name__ == "__main__":
    main()

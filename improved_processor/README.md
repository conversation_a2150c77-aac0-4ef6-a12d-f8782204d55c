# 🎯 智能分层裁剪处理器

基于单模型单任务的智能图像处理工具，按优先级自动选择最佳裁剪方案

## ✨ 核心特色

- 🔄 **智能分层**：脖子 → 肩膀 → 胸部，按优先级自动选择
- 🎯 **单模型单任务**：一个模型专注一个任务，成功即停止，失败即换
- ♾️ **无限队列**：可以无限制添加文件到处理队列
- 🤫 **静默处理**：成功时不打扰，只有失败才通知
- 📊 **详细统计**：实时显示各种检测方法的成功率
- 🎮 **实时控制**：暂停、恢复、清空队列等丰富命令

## 🚀 快速开始

### 方法1：双击启动（推荐）
```
双击 "智能分层裁剪处理器.command" 文件
```

### 方法2：命令行启动
```bash
python3 智能分层裁剪处理器.py
```

### 使用方法
```
启动程序 → 拖拽文件/文件夹到终端 → 自动处理 → 查看结果
```

## 🔄 智能处理流程

```
输入图片 → RMBG-1.4抠图 → 按优先级尝试检测方法 → 成功即裁剪完成
```

### 详细检测顺序
1. **脖子检测（最优先）**
   - Haar Cascades人脸检测 → 推算脖子位置
   - MediaPipe关键点检测 → 精确脖子位置
   - SAM分割分析 → 头部身体分界线

2. **肩膀检测（次优先）**
   - MediaPipe肩膀关键点 → 肩膀位置裁剪
   - SAM轮廓分析 → 肩膀区域分割

3. **胸部检测（最后尝试）**
   - MediaPipe推算胸部位置 → 胸部位置裁剪
   - SAM轮廓分析 → 胸部区域分割

4. **全部失败**
   - 返回处理失败信息

## 🎮 快捷命令

| 命令 | 功能 |
|------|------|
| `status` 或 `st` | 显示队列状态 |
| `stats` | 显示详细统计信息 |
| `pause` | 暂停队列处理 |
| `resume` | 恢复队列处理 |
| `clear` | 清空队列 |
| `open` | 打开结果文件夹 |
| `help` 或 `?` | 显示帮助信息 |
| `quit` 或 `q` | 退出程序 |

## 📊 状态显示

### 队列状态示例
```
🔄 正在处理: image.jpg | 队列: 3 | 完成: 15
   脖子: 12 | 肩膀: 2 | 胸部: 1 | 失败: 0
✅ 队列空闲 | 完成: 18
   脖子: 14 | 肩膀: 3 | 胸部: 1 | 失败: 0
```

### 详细统计示例
```
📊 详细统计信息:
总处理数: 25
失败数: 1

🎯 脖子检测成功:
  Haar Cascades: 15
  MediaPipe: 3
  SAM: 2

👐 肩膀检测成功:
  MediaPipe: 3
  SAM: 1

🫁 胸部检测成功:
  MediaPipe: 0
  SAM: 0
```

## 🎨 支持格式

### 输入格式
- JPG, JPEG, PNG, BMP, TIFF, WebP

### 输出格式
- PNG（保持透明背景）
- JPG（白色背景）

## ⚡ 预期性能

| 检测方法 | 耗时 | 预期成功率 |
|----------|------|------------|
| Haar脖子检测 | 0.1秒 | ~70% |
| MediaPipe脖子检测 | 1-2秒 | ~85% |
| SAM脖子检测 | 3-5秒 | ~90% |
| MediaPipe肩膀检测 | 1-2秒 | ~95% |
| SAM肩膀检测 | 3-5秒 | ~98% |
| MediaPipe胸部检测 | 1-2秒 | ~98% |
| SAM胸部检测 | 3-5秒 | ~99% |
| **总体成功率** | - | **~99%** |

## 🔧 技术架构

### 使用的模型
- **RMBG-1.4** (176MB)：抠图模型，去除背景
- **Haar Cascades**：OpenCV人脸检测，快速脖子推算
- **MediaPipe Pose**：33个关键点检测，精确身体部位定位
- **SAM-B** (375MB)：Meta分割模型，轮廓分析和部位分割

### 核心设计原则
1. **单模型单任务**：每个模型专注一个检测任务
2. **优先级处理**：按脖子→肩膀→胸部的优先级顺序
3. **成功即停止**：任何方法成功就立即完成，不浪费计算
4. **失败即换模型**：当前方法失败立即尝试下一个
5. **智能容错**：多层检测确保极高成功率

## 💡 使用技巧

### 路径输入
- 直接拖拽文件/文件夹到终端窗口
- 自动处理路径中的空格和特殊字符
- 支持中文路径和文件名

### 批量处理
- 可以同时拖拽多个文件夹
- 处理过程中可以继续添加新任务
- 暂停/恢复功能方便控制处理节奏

### 结果分析
- 使用 `stats` 命令查看各种方法的成功率
- 根据统计信息优化图片质量或调整参数

## 🎉 优势特点

1. **智能分层**：多种检测方法确保极高成功率
2. **效率优化**：单模型单任务，避免不必要的计算
3. **用户友好**：静默处理，详细统计，实时控制
4. **扩展性强**：容易添加新的检测模型和方法
5. **逻辑清晰**：每一步都明确，容易调试和维护

---

🎯 **享受智能化的分层图像处理体验！**

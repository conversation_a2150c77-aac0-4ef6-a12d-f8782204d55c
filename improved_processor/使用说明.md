# 🎯 智能分层裁剪处理器 - 使用说明

## 🚀 快速开始

### 启动方式
```bash
# 方法1：双击启动（推荐）
双击 "智能分层裁剪处理器.command" 文件

# 方法2：命令行启动
python3 智能分层裁剪处理器.py
```

## 🔄 智能处理逻辑

### 核心原则
- **单模型单任务**：一次只用一个模型做一件事
- **成功即停止**：任何方法成功就立即完成
- **失败即换模型**：当前方法失败立即尝试下一个
- **优先级处理**：脖子 > 肩膀 > 胸部

### 完整处理流程
```
输入图片
    ↓
RMBG-1.4抠图
    ↓
脖子检测（最优先）
├─ Haar人脸检测 → 成功 → 脖子裁剪 → 完成 ✅
├─ MediaPipe关键点 → 成功 → 脖子裁剪 → 完成 ✅
├─ SAM分割分析 → 成功 → 脖子裁剪 → 完成 ✅
└─ 全部失败 ↓

肩膀检测（次优先）
├─ MediaPipe肩膀关键点 → 成功 → 肩膀裁剪 → 完成 ✅
├─ SAM轮廓分析 → 成功 → 肩膀裁剪 → 完成 ✅
└─ 全部失败 ↓

胸部检测（最后尝试）
├─ MediaPipe推算胸部 → 成功 → 胸部裁剪 → 完成 ✅
├─ SAM轮廓分析 → 成功 → 胸部裁剪 → 完成 ✅
└─ 全部失败 ↓

返回处理失败 ❌
```

## 💡 使用方法

### 基本操作
```
启动程序 → 拖拽文件/文件夹到终端 → 自动处理 → 查看结果
```

### 支持的输入
- **单张图片**：直接拖拽图片文件
- **整个文件夹**：拖拽包含图片的文件夹
- **无限添加**：可以不断添加新任务到队列

### 交互特点
- **静默处理**：成功时不打扰，只有失败才通知
- **实时状态**：随时查看处理进度和统计信息
- **后台处理**：可以继续添加任务

## 🎮 快捷命令详解

### 状态查看
- `status` 或 `st` - 显示当前队列状态和基本统计
- `stats` - 显示详细的各方法成功率统计

### 队列控制
- `pause` - 暂停队列处理（当前任务完成后暂停）
- `resume` - 恢复队列处理
- `clear` - 清空所有等待中的任务

### 其他功能
- `open` - 打开当前工作目录
- `help` 或 `?` - 显示帮助信息
- `quit` 或 `q` - 退出程序

## 📁 输出结构

### 单文件处理
```
原图片目录/
├── original_image.jpg          # 原图片
├── original_image_neck_haar_cropped.png    # 脖子裁剪PNG
├── original_image_neck_haar_cropped.jpg    # 脖子裁剪JPG
└── ...
```

### 批量处理
```
input_folder_processed/
├── png/                        # PNG格式结果
│   ├── image1_neck_mediapipe_cropped.png
│   ├── image2_shoulder_mediapipe_cropped.png
│   └── ...
└── jpg/                        # JPG格式结果
    ├── image1_neck_mediapipe_cropped.jpg
    ├── image2_shoulder_mediapipe_cropped.jpg
    └── ...
```

## 📊 统计信息解读

### 基本状态显示
```
🔄 正在处理: image.jpg | 队列: 3 | 完成: 15
   脖子: 12 | 肩膀: 2 | 胸部: 1 | 失败: 0
```
- **队列**：等待处理的任务数
- **完成**：成功处理的总数
- **脖子/肩膀/胸部**：各部位成功裁剪的数量
- **失败**：完全无法处理的数量

### 详细统计信息
```
📊 详细统计信息:
🎯 脖子检测成功:
  Haar Cascades: 15    # Haar人脸检测成功数
  MediaPipe: 3         # MediaPipe关键点检测成功数
  SAM: 2               # SAM分割分析成功数
```

## 🎨 支持格式

### 输入格式
- JPG, JPEG - 标准照片格式
- PNG - 支持透明背景
- BMP - 位图格式
- TIFF - 高质量图像
- WebP - 现代图像格式

### 输出格式
- **PNG**：保持透明背景，适合进一步编辑
- **JPG**：白色背景，适合直接使用

## 💡 使用技巧

### 提高成功率
1. **图片质量**：使用清晰、光线充足的图片
2. **人物姿态**：正面或侧面效果最好
3. **背景简单**：避免复杂背景干扰检测

### 批量处理建议
1. **分批处理**：大量图片可以分批添加
2. **监控进度**：使用 `status` 命令查看处理进度
3. **暂停恢复**：需要时可以暂停处理，稍后恢复

### 结果优化
1. **查看统计**：使用 `stats` 了解哪种方法效果最好
2. **调整策略**：根据统计结果优化图片质量
3. **重新处理**：失败的图片可以重新拖拽处理

## 🔧 故障排除

### 常见问题

**Q: 程序卡住不动？**
A: 输入 `status` 查看状态，或 `pause` 暂停后 `resume` 恢复

**Q: 处理失败率高？**
A: 检查图片质量，使用 `stats` 查看哪个环节失败最多

**Q: 想重新处理某些图片？**
A: 直接拖拽图片重新处理，程序会自动覆盖之前的结果

**Q: 如何停止处理？**
A: 输入 `pause` 暂停，或 `clear` 清空队列

**Q: MediaPipe或SAM初始化失败？**
A: 检查依赖库是否正确安装，确保模型文件存在

## 🎉 优势特点

1. **智能化**：自动选择最佳检测方法
2. **高效率**：单模型单任务，避免浪费计算
3. **高成功率**：多层检测确保99%+成功率
4. **用户友好**：静默处理，详细反馈
5. **易扩展**：容易添加新的检测方法

---

🎯 **按照这个逻辑，享受智能化的图像处理体验！**

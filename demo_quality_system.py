#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
质量评估系统演示脚本
展示新的检测质量验证、置信度评估和详细报告功能
"""

import sys
import os
from pathlib import Path

# 添加improved_processor到路径
sys.path.append('improved_processor')

def create_demo_image():
    """创建一个简单的演示图像用于测试"""
    try:
        from PIL import Image, ImageDraw
        import numpy as np
        
        # 创建一个简单的人像图像
        width, height = 400, 600
        image = Image.new('RGB', (width, height), (255, 255, 255))
        draw = ImageDraw.Draw(image)
        
        # 画一个简单的人形
        # 头部 (圆形)
        head_center = (width//2, height//4)
        head_radius = 60
        draw.ellipse([
            head_center[0] - head_radius, head_center[1] - head_radius,
            head_center[0] + head_radius, head_center[1] + head_radius
        ], fill=(255, 220, 177))  # 肤色
        
        # 脖子
        neck_top = head_center[1] + head_radius
        neck_bottom = neck_top + 40
        draw.rectangle([
            head_center[0] - 20, neck_top,
            head_center[0] + 20, neck_bottom
        ], fill=(255, 220, 177))
        
        # 肩膀和身体
        shoulder_top = neck_bottom
        shoulder_width = 120
        body_height = 200
        draw.rectangle([
            head_center[0] - shoulder_width//2, shoulder_top,
            head_center[0] + shoulder_width//2, shoulder_top + body_height
        ], fill=(100, 150, 200))  # 衣服颜色
        
        # 保存演示图像
        demo_path = "demo_person.jpg"
        image.save(demo_path, quality=95)
        print(f"✅ 创建演示图像: {demo_path}")
        return demo_path
        
    except Exception as e:
        print(f"❌ 创建演示图像失败: {e}")
        return None

def demo_quality_assessment():
    """演示质量评估系统的完整功能"""
    print("🎭 质量评估系统演示")
    print("=" * 60)
    
    try:
        from 智能分层裁剪处理器 import SmartLayeredProcessor
        
        # 初始化处理器
        print("🔧 初始化处理器...")
        processor = SmartLayeredProcessor()
        
        # 创建演示图像
        demo_image = create_demo_image()
        if not demo_image:
            print("❌ 无法创建演示图像，退出演示")
            return
        
        print(f"\n🖼️  使用演示图像: {demo_image}")
        
        # 演示置信度计算
        print("\n📊 置信度计算演示:")
        print("-" * 40)
        
        # 不同场景的置信度
        scenarios = [
            ("高质量MediaPipe检测", "neck_mediapipe", {'visibility': 0.9}),
            ("中等质量SAM检测", "neck_sam", {'sam_score': 0.7, 'mask_area': 3000}),
            ("低质量Haar检测", "neck_haar", {'face_area_ratio': 0.02, 'face_count': 1}),
            ("优秀SAM检测", "shoulder_sam", {'sam_score': 0.95, 'mask_area': 8000}),
            ("边缘检测", "edge_detection", {}),
        ]
        
        for desc, method, data in scenarios:
            confidence = processor.calculate_detection_confidence(method, True, 100, data)
            print(f"   {desc:<25} 置信度: {confidence:.3f}")
        
        # 演示质量验证
        print("\n🔍 质量验证演示:")
        print("-" * 40)
        
        # 模拟不同的检测结果场景
        test_scenarios = [
            {
                'name': '高一致性场景',
                'results': [
                    ("neck_haar", True, 120, 0.8),
                    ("neck_mediapipe", True, 125, 0.9),
                    ("neck_sam", True, 122, 0.7),
                ]
            },
            {
                'name': '低一致性场景',
                'results': [
                    ("neck_haar", True, 100, 0.6),
                    ("neck_mediapipe", True, 150, 0.8),
                    ("neck_sam", True, 200, 0.5),
                ]
            },
            {
                'name': '单一检测场景',
                'results': [
                    ("neck_haar", False, "人脸过小", 0.0),
                    ("neck_mediapipe", True, 130, 0.9),
                    ("neck_sam", False, "SAM分数过低", 0.0),
                ]
            }
        ]
        
        for scenario in test_scenarios:
            print(f"\n📋 {scenario['name']}:")
            results = scenario['results']
            
            # 显示输入
            for method, success, result, conf in results:
                status = "✅" if success else "❌"
                if success:
                    print(f"     {status} {method}: 位置={result}, 置信度={conf:.3f}")
                else:
                    print(f"     {status} {method}: {result}")
            
            # 模拟质量验证（简化版）
            successful = [(m, r, c) for m, s, r, c in results if s]
            if successful:
                positions = [r for _, r, _ in successful]
                confidences = [c for _, _, c in successful]
                
                if len(positions) > 1:
                    import statistics
                    pos_std = statistics.stdev(positions)
                    avg_pos = statistics.mean(positions)
                    consistency = max(0, 1.0 - (pos_std / avg_pos))
                else:
                    consistency = 1.0
                
                avg_conf = statistics.mean(confidences)
                reliability = consistency * 0.4 + avg_conf * 0.6
                
                print(f"     📊 位置一致性: {consistency:.3f}")
                print(f"     📊 平均置信度: {avg_conf:.3f}")
                print(f"     📊 可靠性评分: {reliability:.3f}")
                
                if reliability >= 0.5:
                    best_method = max(successful, key=lambda x: x[2])[0]
                    print(f"     🎯 推荐: {best_method}")
                else:
                    print(f"     ⚠️  质量不足，建议人工检查")
            else:
                print(f"     ❌ 无成功检测")
        
        # 如果可能，运行真实处理
        print(f"\n🚀 真实处理演示:")
        print("-" * 40)
        
        try:
            success, result = processor.process_single_image_smart(demo_image)
            if success:
                print(f"✅ 处理成功!")
            else:
                print(f"❌ 处理失败: {result}")
        except Exception as e:
            print(f"❌ 真实处理出错: {e}")
        
        # 清理演示文件
        if os.path.exists(demo_image):
            os.remove(demo_image)
            print(f"\n🧹 清理演示文件: {demo_image}")
        
        print(f"\n🎉 演示完成!")
        
    except Exception as e:
        print(f"❌ 演示失败: {e}")
        import traceback
        traceback.print_exc()

if __name__ == "__main__":
    demo_quality_assessment()
